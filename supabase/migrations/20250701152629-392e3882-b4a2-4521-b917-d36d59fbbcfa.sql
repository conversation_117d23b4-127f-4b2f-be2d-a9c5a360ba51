
-- First, let's drop the problematic policy
DROP POLICY IF EXISTS "Ad<PERSON> can view admin users" ON admin_users;

-- Create a new policy that doesn't cause recursion
-- This allows admins to view admin users by checking their user_id directly
CREATE POLICY "Admins can view admin users" ON admin_users
FOR SELECT 
USING (user_id = auth.uid());

-- Also let's update the blog_posts policy to be more direct
DROP POLICY IF EXISTS "Ad<PERSON> can manage blog posts" ON blog_posts;
CREATE POLICY "Ad<PERSON> can manage blog posts" ON blog_posts
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users 
    WHERE admin_users.user_id = auth.uid()
  )
);

-- Update trek_packages policy similarly
DROP POLICY IF EXISTS "Ad<PERSON> can manage trek packages" ON trek_packages;
CREATE POLICY "Ad<PERSON> can manage trek packages" ON trek_packages
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users 
    WHERE admin_users.user_id = auth.uid()
  )
);
