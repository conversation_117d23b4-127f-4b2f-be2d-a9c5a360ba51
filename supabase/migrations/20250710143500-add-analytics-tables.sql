-- Enable UUID generation if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Analytics Data Table
CREATE TABLE analytics_data (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    date DATE NOT NULL,
    page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    bounce_rate NUMERIC,
    avg_session_duration INTEGER, -- in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Page Analytics Table
CREATE TABLE page_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    page_path TEXT NOT NULL,
    page_title TEXT,
    content_type TEXT CHECK (content_type IN ('blog', 'trek', 'page', 'other')),
    content_id UUID,
    views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    avg_time_on_page INTEGER, -- in seconds
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Traffic Sources Table
CREATE TABLE traffic_sources (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    date DATE NOT NULL,
    source TEXT NOT NULL,
    medium TEXT,
    campaign TEXT,
    visitors INTEGER DEFAULT 0,
    page_views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Device Analytics Table
CREATE TABLE device_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    date DATE NOT NULL,
    device_type TEXT CHECK (device_type IN ('desktop', 'mobile', 'tablet', 'other')),
    browser TEXT,
    operating_system TEXT,
    visitors INTEGER DEFAULT 0,
    page_views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Conversion Analytics Table
CREATE TABLE conversion_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    date DATE NOT NULL,
    conversion_type TEXT CHECK (conversion_type IN ('booking', 'newsletter', 'contact', 'other')),
    source_page TEXT,
    conversions INTEGER DEFAULT 0,
    conversion_rate NUMERIC,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Content Performance Table
CREATE TABLE content_performance (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_type TEXT CHECK (content_type IN ('blog', 'trek')),
    content_id UUID NOT NULL,
    views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    avg_time_on_page INTEGER, -- in seconds
    social_shares INTEGER DEFAULT 0,
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add performance indexes
CREATE INDEX idx_blog_posts_published ON blog_posts(published, created_at);
CREATE INDEX idx_trek_packages_featured ON trek_packages(featured, created_at);
CREATE INDEX idx_booking_inquiries_status ON booking_inquiries(status, created_at);
CREATE INDEX idx_page_analytics_path ON page_analytics(page_path, date);
CREATE INDEX idx_traffic_sources_date ON traffic_sources(date, source);
CREATE INDEX idx_device_analytics_date ON device_analytics(date, device_type);
CREATE INDEX idx_content_performance_content ON content_performance(content_type, content_id);

-- Enable RLS on all tables
ALTER TABLE analytics_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE traffic_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversion_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_performance ENABLE ROW LEVEL SECURITY;

-- RLS Policies for admin access
CREATE POLICY "Admins can manage analytics data" ON analytics_data FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

CREATE POLICY "Admins can manage page analytics" ON page_analytics FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

CREATE POLICY "Admins can manage traffic sources" ON traffic_sources FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

CREATE POLICY "Admins can manage device analytics" ON device_analytics FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

CREATE POLICY "Admins can manage conversion analytics" ON conversion_analytics FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

CREATE POLICY "Admins can manage content performance" ON content_performance FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Sample data for testing
INSERT INTO analytics_data (date, page_views, unique_visitors, bounce_rate, avg_session_duration)
VALUES 
  (CURRENT_DATE - INTERVAL '1 day', 1250, 850, 45.5, 180),
  (CURRENT_DATE - INTERVAL '2 day', 1100, 750, 48.2, 165),
  (CURRENT_DATE - INTERVAL '3 day', 1300, 900, 42.8, 195);

-- Sample page analytics
INSERT INTO page_analytics (page_path, page_title, content_type, views, unique_visitors, avg_time_on_page, date)
VALUES 
  ('/treks/everest-base-camp', 'Everest Base Camp Trek', 'trek', 450, 380, 240, CURRENT_DATE - INTERVAL '1 day'),
  ('/blog/best-time-to-visit-nepal', 'Best Time to Visit Nepal', 'blog', 320, 280, 180, CURRENT_DATE - INTERVAL '1 day'),
  ('/treks/annapurna-circuit', 'Annapurna Circuit Trek', 'trek', 380, 320, 210, CURRENT_DATE - INTERVAL '1 day');

-- Sample traffic sources
INSERT INTO traffic_sources (date, source, medium, visitors, page_views)
VALUES 
  (CURRENT_DATE - INTERVAL '1 day', 'google', 'organic', 450, 650),
  (CURRENT_DATE - INTERVAL '1 day', 'facebook', 'social', 180, 250),
  (CURRENT_DATE - INTERVAL '1 day', 'direct', 'none', 220, 350);

-- Sample device analytics
INSERT INTO device_analytics (date, device_type, browser, operating_system, visitors, page_views)
VALUES 
  (CURRENT_DATE - INTERVAL '1 day', 'desktop', 'Chrome', 'Windows', 380, 580),
  (CURRENT_DATE - INTERVAL '1 day', 'mobile', 'Safari', 'iOS', 420, 620),
  (CURRENT_DATE - INTERVAL '1 day', 'tablet', 'Safari', 'iPadOS', 50, 80);
