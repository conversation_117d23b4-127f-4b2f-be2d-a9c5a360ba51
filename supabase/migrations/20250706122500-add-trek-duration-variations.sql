-- Add duration variations support to trek_packages
ALTER TABLE trek_packages
ADD COLUMN minimum_days INTEGER,
ADD COLUMN duration_variations JSONB DEFAULT '{"variations": []}'::jsonb,
ADD CONSTRAINT minimum_days_check CHECK (minimum_days > 0);

-- Function to validate duration variations format and constraints
CREATE OR REPLACE FUNCTION validate_trek_duration_variations()
RETURNS TRIGGER AS $$
BEGIN
  -- Ensure each variation has required fields
  IF NOT (
    SELECT bool_and(
      jsonb_typeof(variation->'days') = 'number' AND
      jsonb_typeof(variation->'price_usd') = 'number' AND
      jsonb_typeof(variation->'itinerary') = 'array'
    )
    FROM jsonb_array_elements(NEW.duration_variations->'variations') as variation
  )
  THEN
    RAISE EXCEPTION 'Invalid duration variation format';
  END IF;
  
  -- Ensure all variations meet minimum days
  IF EXISTS (
    SELECT 1
    FROM jsonb_array_elements(NEW.duration_variations->'variations') as variation
    WHERE (variation->>'days')::integer < NEW.minimum_days
  )
  THEN
    RAISE EXCEPTION 'Duration variation days cannot be less than minimum_days';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate duration variations on insert/update
CREATE TRIGGER check_trek_duration_variations
BEFORE INSERT OR UPDATE ON trek_packages
FOR EACH ROW
WHEN (NEW.duration_variations IS NOT NULL)
EXECUTE FUNCTION validate_trek_duration_variations();

-- Update existing trek packages with default minimum days
UPDATE trek_packages
SET minimum_days = duration_days
WHERE minimum_days IS NULL;

-- Add not null constraint after setting defaults
ALTER TABLE trek_packages
ALTER COLUMN minimum_days SET NOT NULL;

-- Add index for better query performance
CREATE INDEX idx_trek_packages_duration_variations ON trek_packages USING gin (duration_variations);

-- Update policy to allow admins to manage duration variations
DROP POLICY IF EXISTS "Admins can manage trek packages" ON trek_packages;
CREATE POLICY "Admins can manage trek packages" ON trek_packages 
FOR ALL USING (EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid()));
