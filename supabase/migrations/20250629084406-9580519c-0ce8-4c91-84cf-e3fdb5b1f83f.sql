
-- Add SEO optimization columns to blog_posts table
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS optimized_title TEXT;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS meta_description TEXT;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS seo_tags TEXT[] DEFAULT '{}';
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS last_optimized_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS optimization_count INTEGER DEFAULT 0;

-- Create SEO optimization logs table
CREATE TABLE IF NOT EXISTS seo_optimization_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  original_title TEXT,
  optimized_title TEXT,
  original_content TEXT,
  optimized_content TEXT,
  original_meta_description TEXT,
  optimized_meta_description TEXT,
  original_tags TEXT[],
  optimized_tags TEXT[],
  optimization_type TEXT NOT NULL CHECK (optimization_type IN ('manual', 'auto_create', 'auto_background')),
  gemini_response JSONB,
  status TEXT NOT NULL CHECK (status IN ('success', 'failed', 'no_update_needed')),
  error_message TEXT,
  processing_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on SEO logs table
ALTER TABLE seo_optimization_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for admins to view SEO logs
CREATE POLICY "Admins can view SEO optimization logs" ON seo_optimization_logs FOR SELECT USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

-- Create policy for system to insert SEO logs
CREATE POLICY "System can insert SEO optimization logs" ON seo_optimization_logs FOR INSERT WITH CHECK (true);

-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create a function to invoke the SEO optimizer
CREATE OR REPLACE FUNCTION invoke_seo_optimizer()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This will be called by the cron job to invoke the edge function
  PERFORM net.http_post(
    url := 'https://jddvriaujdxiifrmksbl.supabase.co/functions/v1/seo-optimizer',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpkZHZyaWF1amR4aWlmcm1rc2JsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODQ0MjgsImV4cCI6MjA2Njc2MDQyOH0.toNbpHpnLvVFbQYTYND8zQRZ9YltZza8BUr-rj3xoHQ"}'::jsonb,
    body := '{"trigger": "scheduled"}'::jsonb
  );
END;
$$;

-- Schedule the SEO optimizer to run every 3 hours
SELECT cron.schedule(
  'seo-optimizer-background',
  '0 */3 * * *', -- Every 3 hours
  'SELECT invoke_seo_optimizer();'
);
