-- Enable UUID generation if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Blog Drafts Table
CREATE TABLE blog_drafts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    blog_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    cover_image TEXT,
    tags TEXT[] DEFAULT '{}',
    seo_data JSONB DEFAULT '{}'::jsonb,
    last_saved_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Blog Versions Table
CREATE TABLE blog_versions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    blog_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    cover_image TEXT,
    tags TEXT[] DEFAULT '{}',
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- SEO A/B Tests Table
CREATE TABLE seo_ab_tests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    blog_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    variant_a TEXT NOT NULL,
    variant_b TEXT NOT NULL,
    test_type TEXT CHECK (test_type IN ('title', 'meta_description', 'excerpt')) NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
    end_date TIMESTAMP WITH TIME ZONE,
    winner_variant TEXT CHECK (winner_variant IN ('a', 'b')),
    metrics JSONB DEFAULT '{
        "a": {"views": 0, "clicks": 0, "conversions": 0},
        "b": {"views": 0, "clicks": 0, "conversions": 0}
    }'::jsonb,
    status TEXT CHECK (status IN ('running', 'completed', 'stopped')) DEFAULT 'running',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Blog Performance Metrics Table
CREATE TABLE blog_performance_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    blog_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    avg_time_on_page NUMERIC DEFAULT 0,
    bounce_rate NUMERIC DEFAULT 0,
    social_shares JSONB DEFAULT '{
        "facebook": 0,
        "twitter": 0,
        "linkedin": 0
    }'::jsonb,
    seo_score NUMERIC CHECK (seo_score BETWEEN 0 AND 100),
    keyword_rankings JSONB DEFAULT '[]'::jsonb,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better query performance
CREATE INDEX idx_blog_drafts_user_id ON blog_drafts(user_id);
CREATE INDEX idx_blog_drafts_blog_id ON blog_drafts(blog_id);
CREATE INDEX idx_blog_versions_blog_id ON blog_versions(blog_id);
CREATE INDEX idx_seo_ab_tests_blog_id ON seo_ab_tests(blog_id);
CREATE INDEX idx_blog_performance_blog_id ON blog_performance_metrics(blog_id);
CREATE INDEX idx_blog_drafts_updated ON blog_drafts(updated_at DESC);

-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_blog_drafts_updated_at
    BEFORE UPDATE ON blog_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_seo_ab_tests_updated_at
    BEFORE UPDATE ON seo_ab_tests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
