
-- Create enum types
CREATE TYPE booking_status AS ENUM ('pending', 'contacted', 'confirmed', 'cancelled');
CREATE TYPE admin_role AS ENUM ('admin', 'editor');

-- Trek packages table
CREATE TABLE trek_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  region TEXT NOT NULL,
  short_description TEXT NOT NULL,
  long_description TEXT NOT NULL,
  itinerary JSONB NOT NULL DEFAULT '[]',
  gallery TEXT[] DEFAULT '{}',
  featured BOOLEAN DEFAULT false,
  price_usd INTEGER,
  duration_days INTEGER,
  difficulty_level TEXT,
  max_altitude INTEGER,
  best_season TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Blog posts table
CREATE TABLE blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content TEXT NOT NULL,
  cover_image TEXT,
  excerpt TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  published BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Testimonials table
CREATE TABLE testimonials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  quote TEXT NOT NULL,
  avatar TEXT,
  rating INTEGER DEFAULT 5,
  trek_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Booking inquiries table
CREATE TABLE booking_inquiries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT NOT NULL,
  trek_name TEXT NOT NULL,
  preferred_dates TEXT,
  message TEXT,
  status booking_status DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Admin users table
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  role admin_role DEFAULT 'editor',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- SEO data table
CREATE TABLE seo_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  page_type TEXT NOT NULL,
  slug TEXT,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  keywords TEXT[] DEFAULT '{}',
  schema_json JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(page_type, slug)
);

-- Enable RLS on all tables
ALTER TABLE trek_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE testimonials ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_data ENABLE ROW LEVEL SECURITY;

-- RLS Policies for public read access
CREATE POLICY "Anyone can view published trek packages" ON trek_packages FOR SELECT USING (true);
CREATE POLICY "Anyone can view published blog posts" ON blog_posts FOR SELECT USING (published = true);
CREATE POLICY "Anyone can view testimonials" ON testimonials FOR SELECT USING (true);
CREATE POLICY "Anyone can view SEO data" ON seo_data FOR SELECT USING (true);

-- RLS Policies for booking inquiries
CREATE POLICY "Anyone can insert booking inquiries" ON booking_inquiries FOR INSERT WITH CHECK (true);
CREATE POLICY "Admins can view all booking inquiries" ON booking_inquiries FOR SELECT USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can update booking inquiries" ON booking_inquiries FOR UPDATE USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

-- RLS Policies for admin operations
CREATE POLICY "Admins can manage trek packages" ON trek_packages FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can manage blog posts" ON blog_posts FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can manage testimonials" ON testimonials FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can manage SEO data" ON seo_data FOR ALL USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can view admin users" ON admin_users FOR SELECT USING (
  EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('trek-images', 'trek-images', true),
  ('blog-images', 'blog-images', true),
  ('avatars', 'avatars', true);

-- Storage policies
CREATE POLICY "Anyone can view trek images" ON storage.objects FOR SELECT USING (bucket_id = 'trek-images');
CREATE POLICY "Anyone can view blog images" ON storage.objects FOR SELECT USING (bucket_id = 'blog-images');
CREATE POLICY "Anyone can view avatars" ON storage.objects FOR SELECT USING (bucket_id = 'avatars');
CREATE POLICY "Admins can upload trek images" ON storage.objects FOR INSERT WITH CHECK (
  bucket_id = 'trek-images' AND EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can upload blog images" ON storage.objects FOR INSERT WITH CHECK (
  bucket_id = 'blog-images' AND EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);
CREATE POLICY "Admins can upload avatars" ON storage.objects FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND EXISTS (SELECT 1 FROM admin_users WHERE user_id = auth.uid())
);

-- Insert dummy data for trek packages
INSERT INTO trek_packages (name, slug, region, short_description, long_description, price_usd, duration_days, difficulty_level, max_altitude, best_season, featured, itinerary) VALUES
(
  'Everest Base Camp Trek',
  'everest-base-camp-trek',
  'Everest Region',
  'Experience the ultimate adventure to the base of the world''s highest mountain',
  'The Everest Base Camp Trek is a once-in-a-lifetime adventure that takes you through the heart of the Khumbu region to the base of Mount Everest. This iconic trek offers breathtaking mountain views, rich Sherpa culture, and the chance to walk in the footsteps of legendary mountaineers.',
  1299,
  14,
  'Moderate to Challenging',
  5364,
  'Spring & Autumn',
  true,
  '[
    {"day": 1, "title": "Fly to Lukla, Trek to Phakding", "description": "Early morning flight to Lukla, begin trek to Phakding"},
    {"day": 2, "title": "Phakding to Namche Bazaar", "description": "Cross suspension bridges and climb to the famous Sherpa capital"},
    {"day": 3, "title": "Acclimatization Day in Namche", "description": "Rest day with optional hike to Everest View Hotel"},
    {"day": 4, "title": "Namche to Tengboche", "description": "Trek through rhododendron forests to the famous monastery"}
  ]'::jsonb
),
(
  'Annapurna Circuit Trek',
  'annapurna-circuit-trek',
  'Annapurna Region',
  'Classic circuit trek with diverse landscapes and mountain passes',
  'The Annapurna Circuit is one of Nepal''s most popular treks, offering incredible diversity in landscapes, cultures, and ecosystems. From subtropical valleys to high-altitude deserts, this trek showcases the best of the Himalayas.',
  899,
  16,
  'Moderate',
  5416,
  'Spring & Autumn',
  true,
  '[
    {"day": 1, "title": "Drive to Besisahar, Trek to Bhulbhule", "description": "Start the classic circuit trek"},
    {"day": 2, "title": "Bhulbhule to Jagat", "description": "Trek through terraced fields and traditional villages"}
  ]'::jsonb
),
(
  'Langtang Valley Trek',
  'langtang-valley-trek',
  'Langtang Region',
  'Discover the beautiful valley closest to Kathmandu',
  'The Langtang Valley Trek offers stunning mountain views, rich Tamang culture, and beautiful rhododendron forests. Known as the "Valley of Glaciers," this trek is perfect for those seeking adventure without the crowds of more popular routes.',
  699,
  10,
  'Easy to Moderate',
  4984,
  'Spring & Autumn',
  false,
  '[
    {"day": 1, "title": "Drive to Syabrubesi", "description": "Scenic drive from Kathmandu"},
    {"day": 2, "title": "Syabrubesi to Lama Hotel", "description": "Trek through oak and rhododendron forests"}
  ]'::jsonb
),
(
  'Manaslu Circuit Trek',
  'manaslu-circuit-trek',
  'Manaslu Region',
  'Off-the-beaten-path adventure around the eighth highest mountain',
  'The Manaslu Circuit Trek is a spectacular journey around Mount Manaslu, the eighth highest peak in the world. This restricted area trek offers pristine mountain wilderness, authentic Tibetan Buddhist culture, and fewer crowds than other popular circuits.',
  1199,
  18,
  'Challenging',
  5213,
  'Spring & Autumn',
  true,
  '[
    {"day": 1, "title": "Drive to Soti Khola", "description": "Begin the journey to remote Manaslu region"},
    {"day": 2, "title": "Soti Khola to Machha Khola", "description": "Trek along the Budhi Gandaki river"}
  ]'::jsonb
);

-- Insert dummy data for blog posts
INSERT INTO blog_posts (title, slug, content, cover_image, excerpt, tags, published) VALUES
(
  'Essential Packing Guide for Everest Base Camp Trek',
  'essential-packing-guide-everest-base-camp',
  'Preparing for the Everest Base Camp trek requires careful planning and the right equipment. Here''s your comprehensive packing guide to ensure you''re well-prepared for this incredible journey...',
  'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=800&h=600&fit=crop',
  'Complete packing checklist and essential gear recommendations for your EBC adventure',
  ARRAY['packing', 'everest', 'trekking-gear', 'preparation'],
  true
),
(
  'Best Time to Trek in Nepal: A Season-by-Season Guide',
  'best-time-to-trek-nepal-seasons',
  'Nepal offers incredible trekking opportunities year-round, but choosing the right season can make or break your adventure. Here''s everything you need to know about trekking seasons in Nepal...',
  'https://images.unsplash.com/photo-1472396961693-142e6e269027?w=800&h=600&fit=crop',
  'Discover the perfect time to visit Nepal for your trekking adventure',
  ARRAY['seasons', 'weather', 'planning', 'nepal'],
  true
),
(
  'Altitude Sickness Prevention and Treatment',
  'altitude-sickness-prevention-treatment',
  'Altitude sickness is a serious concern for trekkers in Nepal. Understanding the symptoms, prevention methods, and treatment options is crucial for a safe and enjoyable trek...',
  'https://images.unsplash.com/photo-1482938289607-e9573fc25ebb?w=800&h=600&fit=crop',
  'Essential guide to staying safe at high altitudes during your Nepal trek',
  ARRAY['health', 'safety', 'altitude', 'preparation'],
  true
);

-- Insert dummy data for testimonials
INSERT INTO testimonials (name, country, quote, rating, trek_name) VALUES
(
  'Sarah Johnson',
  'United States',
  'TrekNepalX made my Everest Base Camp dream come true! Their local guides were incredibly knowledgeable and the whole experience was perfectly organized. I felt safe and supported throughout the entire journey.',
  5,
  'Everest Base Camp Trek'
),
(
  'Marco Rodriguez',
  'Spain',
  'The Annapurna Circuit with TrekNepalX was absolutely incredible. The diversity of landscapes and cultures we experienced was beyond my expectations. Highly recommend their services!',
  5,
  'Annapurna Circuit Trek'
),
(
  'Emma Thompson',
  'United Kingdom',
  'As a solo female traveler, I was initially nervous about trekking in Nepal. TrekNepalX''s team made me feel completely comfortable and secure. The Langtang Valley trek was breathtaking!',
  5,
  'Langtang Valley Trek'
),
(
  'David Kim',
  'South Korea',
  'Professional, friendly, and extremely knowledgeable. TrekNepalX delivered an unforgettable Manaslu Circuit experience. The local insights and cultural connections made all the difference.',
  5,
  'Manaslu Circuit Trek'
),
(
  'Lisa Weber',
  'Germany',
  'Best trekking company in Nepal! Their attention to detail and commitment to safety is outstanding. Already planning my next adventure with them.',
  5,
  'Everest Base Camp Trek'
);

-- Insert SEO data
INSERT INTO seo_data (page_type, slug, title, description, keywords) VALUES
(
  'home',
  NULL,
  'TrekNepalX - Premium Trekking & Expeditions in Nepal | Everest Base Camp & More',
  'Discover the heart of the Himalayas with TrekNepalX. Expert-guided treks to Everest Base Camp, Annapurna Circuit, and more. Custom & group adventures with local expertise.',
  ARRAY['nepal trekking', 'everest base camp', 'himalaya tours', 'nepal expeditions', 'mountain trekking']
),
(
  'trek',
  'everest-base-camp-trek',
  'Everest Base Camp Trek - 14 Days | TrekNepalX',
  'Join our expertly guided 14-day Everest Base Camp trek. Experience breathtaking Himalayan views, Sherpa culture, and the adventure of a lifetime with TrekNepalX.',
  ARRAY['everest base camp trek', 'EBC trek', 'nepal trekking', 'himalaya adventure']
),
(
  'blog',
  NULL,
  'Trekking Blog & Guides | TrekNepalX Nepal Adventures',
  'Expert trekking guides, tips, and stories from the Himalayas. Get insider knowledge for your Nepal trekking adventure from our experienced local guides.',
  ARRAY['nepal trekking blog', 'himalaya guides', 'trekking tips', 'nepal travel advice']
);
