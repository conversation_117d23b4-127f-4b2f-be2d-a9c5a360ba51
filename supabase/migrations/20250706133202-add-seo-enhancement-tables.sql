-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Content Calendar
CREATE TABLE content_calendar (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    suggested_date TIMESTAMP WITH TIME ZONE NOT NULL,
    content_type TEXT CHECK (content_type IN ('blog', 'trek')) NOT NULL,
    topic TEXT NOT NULL,
    keywords TEXT[] NOT NULL DEFAULT '{}',
    seasonality_score NUMERIC CHECK (seasonality_score BETWEEN 0 AND 1),
    predicted_engagement NUMERIC CHECK (predicted_engagement BETWEEN 0 AND 1),
    status TEXT CHECK (status IN ('suggested', 'approved', 'published')) DEFAULT 'suggested',
    ai_reasoning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Image SEO Data
CREATE TABLE image_seo_data (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    image_url TEXT NOT NULL,
    alt_text TEXT,
    suggested_filename TEXT,
    optimization_status TEXT CHECK (optimization_status IN ('pending', 'optimized')) DEFAULT 'pending',
    compression_needed BOOLEAN DEFAULT false,
    content_type TEXT CHECK (content_type IN ('blog', 'trek')) NOT NULL,
    content_id UUID NOT NULL,
    vision_api_tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Social SEO Metrics
CREATE TABLE social_seo_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID NOT NULL,
    platform TEXT CHECK (platform IN ('facebook', 'instagram', 'twitter')) NOT NULL,
    preview_text TEXT,
    suggested_hashtags TEXT[] DEFAULT '{}',
    og_tags JSONB DEFAULT '{}'::jsonb,
    engagement_metrics JSONB DEFAULT '{
        "shares": 0,
        "likes": 0,
        "clicks": 0
    }'::jsonb,
    seo_impact_score NUMERIC CHECK (seo_impact_score BETWEEN 0 AND 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Schema Registry
CREATE TABLE schema_registry (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    page_url TEXT NOT NULL,
    schema_type TEXT CHECK (schema_type IN ('TrekPackage', 'BlogPosting', 'FAQPage', 'LocalBusiness')) NOT NULL,
    schema_data JSONB NOT NULL DEFAULT '{}'::jsonb,
    last_validated TIMESTAMP WITH TIME ZONE,
    validation_errors TEXT[] DEFAULT '{}',
    auto_update BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Content Freshness
CREATE TABLE content_freshness (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID NOT NULL,
    content_type TEXT CHECK (content_type IN ('blog', 'trek')) NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    freshness_score NUMERIC CHECK (freshness_score BETWEEN 0 AND 1),
    update_priority TEXT CHECK (update_priority IN ('low', 'medium', 'high')) DEFAULT 'low',
    suggested_updates JSONB DEFAULT '[]'::jsonb,
    seasonal_relevance BOOLEAN DEFAULT false,
    next_review_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE image_seo_data
    ADD CONSTRAINT fk_content
    FOREIGN KEY (content_id) 
    REFERENCES blog_posts(id) 
    ON DELETE CASCADE;

ALTER TABLE social_seo_metrics
    ADD CONSTRAINT fk_content
    FOREIGN KEY (content_id) 
    REFERENCES blog_posts(id) 
    ON DELETE CASCADE;

ALTER TABLE content_freshness
    ADD CONSTRAINT fk_content
    FOREIGN KEY (content_id) 
    REFERENCES blog_posts(id) 
    ON DELETE CASCADE;

-- Add indexes for better query performance
CREATE INDEX idx_content_calendar_date ON content_calendar(suggested_date);
CREATE INDEX idx_content_calendar_status ON content_calendar(status);
CREATE INDEX idx_image_seo_content ON image_seo_data(content_id);
CREATE INDEX idx_social_seo_content ON social_seo_metrics(content_id);
CREATE INDEX idx_schema_registry_url ON schema_registry(page_url);
CREATE INDEX idx_content_freshness_score ON content_freshness(freshness_score);

-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_content_calendar_updated_at
    BEFORE UPDATE ON content_calendar
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_image_seo_updated_at
    BEFORE UPDATE ON image_seo_data
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_seo_updated_at
    BEFORE UPDATE ON social_seo_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schema_registry_updated_at
    BEFORE UPDATE ON schema_registry
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_freshness_updated_at
    BEFORE UPDATE ON content_freshness
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
