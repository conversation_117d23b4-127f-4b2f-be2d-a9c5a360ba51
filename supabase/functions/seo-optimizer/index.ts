/// <reference lib="deno.ns" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import {
  Database,
  BlogPost,
  TrekPackage,
  OptimizationLog,
  RealTimeAnalysisPayload,
  RealTimeAnalysisResponse,
  SEOSuggestion
} from './types.ts';
import {
  calculateReadability,
  calculateKeywordDensity,
  computeOverallScore,
  generateAdvancedSuggestions
} from './utils.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

type GeminiResponse<T> = {
  candidates?: Array<{
    content?: {
      parts?: Array<{
        text: string;
      }>;
    };
  }>;
  error?: T;
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse request body once
    const payload = await req.json().catch(() => ({})) as Record<string, unknown>;
    const {
      content,
      title,
      excerpt = '',
      tags = [],
      blogId,
      trigger = 'manual',
      blogPostId,
      trekId
    } = payload as RealTimeAnalysisPayload & {
      trigger?: string;
      blogPostId?: string;
      trekId?: string;
    };

    // ----- REAL-TIME ANALYSIS PATH -----
    // If we have content and title, this is a real-time analysis request
    if (typeof content === 'string' && typeof title === 'string') {
      try {
        console.log('Processing real-time SEO analysis request');

        const tagsArray = Array.isArray(tags) ? tags : [];
        const excerptStr = typeof excerpt === 'string' ? excerpt : '';

        // Calculate metrics
        const readability = calculateReadability(content);
        const keywordDensity = calculateKeywordDensity(content, tagsArray);
        const wordCount = (content.trim().match(/\b\w+\b/g) || []).length;

        // Generate suggestions
        const suggestions = generateAdvancedSuggestions(content, title, excerptStr, tagsArray);

        // Calculate overall score
        const score = computeOverallScore(readability, keywordDensity, title, excerptStr);

        const response: RealTimeAnalysisResponse = {
          suggestions,
          score,
          metrics: {
            readability,
            keywordDensity,
            wordCount,
            titleLength: title.length,
            metaLength: excerptStr.length
          }
        };

        console.log(`Real-time analysis completed: score=${score}, suggestions=${suggestions.length}`);

        return new Response(JSON.stringify(response), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      } catch (error) {
        console.error('Real-time SEO analysis error:', error);
        return new Response(
          JSON.stringify({
            error: 'Analysis failed',
            suggestions: [],
            score: 0
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    }

    // ----- BATCH/SCHEDULED OPTIMIZATION PATH -----
    // Continue with existing logic for batch processing
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    console.log(`SEO optimization triggered: ${trigger}`);

    // Handle blog posts optimization
    let blogPosts: BlogPost[] = [];
    if (blogPostId) {
      const { data, error } = await supabaseClient
        .from('blog_posts')
        .select('*')
        .eq('id', blogPostId)
        .single();

      if (error) throw error;
      if (data) blogPosts = [data];
    } else if (trigger === 'scheduled') {
      const threeHoursAgo = new Date();
      threeHoursAgo.setHours(threeHoursAgo.getHours() - 3);

      const { data, error } = await supabaseClient
        .from('blog_posts')
        .select('*')
        .eq('published', true)
        .or(`last_optimized_at.is.null,last_optimized_at.lt.${threeHoursAgo.toISOString()}`)
        .limit(5);

      if (error) throw error;
      blogPosts = data || [];
    }

    // Handle trek packages optimization
    let trekPackages: TrekPackage[] = [];
    if (trekId) {
      const { data, error } = await supabaseClient
        .from('trek_packages')
        .select('*')
        .eq('id', trekId)
        .single();

      if (error) throw error;
      if (data) trekPackages = [data];
    } else if (trigger === 'scheduled') {
      const { data, error } = await supabaseClient
        .from('trek_packages')
        .select('*')
        .is('last_optimized_at', null)
        .limit(3);

      if (error) throw error;
      trekPackages = data || [];
    }

    if (blogPosts.length === 0 && trekPackages.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No content to optimize', processed: 0 }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let processedCount = 0;
    let successCount = 0;

    // Process blog posts
    for (const post of blogPosts) {
      const startTime = Date.now();
      try {
        console.log(`Optimizing blog post: ${post.title}`);
        const isReOptimization = post.last_optimized_at !== null;

        const optimizationPrompt = `
You are an SEO expert specializing in travel and trekking content. ${isReOptimization ? 'Review this previously optimized blog post and determine if further SEO improvements are needed.' : 'Optimize the following blog post for better search engine visibility and readability while maintaining the original voice and structure.'}

Current Title: ${post.title}
Current Excerpt: ${post.excerpt}
Current Content: ${post.content}
Current Tags: ${post.tags?.join(', ') || 'None'}
${post.optimized_title ? `Previous Optimized Title: ${post.optimized_title}` : ''}
${post.meta_description ? `Previous Meta Description: ${post.meta_description}` : ''}
${post.seo_tags ? `Previous SEO Tags: ${post.seo_tags.join(', ')}` : ''}

${isReOptimization ?
`IMPORTANT: Only suggest improvements if the content can be significantly enhanced. If the current optimization is sufficient, respond with exactly: {"no_update_needed": true, "reason": "Content is already well optimized"}` :
`Please provide:`}

1. An improved, SEO-friendly title (60 characters or less)
2. An optimized meta description/excerpt (150-160 characters)
3. Enhanced content with better readability, relevant keywords naturally integrated, and improved structure
4. 5-8 relevant SEO tags related to Nepal trekking

Return ONLY a JSON object with this structure:
{
  "title": "optimized title",
  "excerpt": "optimized excerpt",
  "content": "optimized content",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"]
}`;

        const geminiResponse = await fetch(
          `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${geminiApiKey}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              contents: [{
                parts: [{ text: optimizationPrompt }]
              }],
              generationConfig: {
                temperature: 0.3,
                topK: 20,
                topP: 0.8,
                maxOutputTokens: 2048,
              },
              safetySettings: [
                {
                  category: 'HARM_CATEGORY_HARASSMENT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_HATE_SPEECH',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                },
                {
                  category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                  threshold: 'BLOCK_MEDIUM_AND_ABOVE'
                }
              ]
            })
          }
        );

        if (!geminiResponse.ok) {
          const errorText = await geminiResponse.text();
          throw new Error(`Gemini API error: ${errorText}`);
        }

        const geminiData = await geminiResponse.json() as GeminiResponse<unknown>;
        const optimizedContent = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!optimizedContent) {
          throw new Error('No content received from Gemini');
        }

        let optimizedData: Record<string, unknown>;
        try {
          const jsonMatch = optimizedContent.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No JSON found in response');
          }
          optimizedData = JSON.parse(jsonMatch[0]);
        } catch (parseError) {
          throw new Error(`Parse error: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
        }

        if ('no_update_needed' in optimizedData && optimizedData.no_update_needed === true) {
          console.log(`No update needed for post ${post.id}: ${optimizedData.reason}`);
          await logOptimization(supabaseClient, 'blog', post.id, {
            status: 'no_update_needed',
            original_title: post.title,
            gemini_response: optimizedData,
            error_message: optimizedData.reason as string,
            processing_time_ms: Date.now() - startTime,
            optimization_type: trigger
          });
          processedCount++;
          continue;
        }

        const updateData = {
          optimized_title: optimizedData.title as string || post.title,
          meta_description: optimizedData.excerpt as string || post.excerpt,
          seo_tags: (optimizedData.tags as string[]) || post.tags || [],
          last_optimized_at: new Date().toISOString(),
          optimization_count: (post.optimization_count || 0) + 1,
          updated_at: new Date().toISOString()
        };

        const finalUpdateData = {
          ...updateData,
          ...((!post.last_optimized_at || trigger === 'manual') ? {
            title: optimizedData.title as string || post.title,
            content: optimizedData.content as string || post.content,
            excerpt: optimizedData.excerpt as string || post.excerpt,
          } : {})
        };

        const { error: updateError } = await supabaseClient
          .from('blog_posts')
          .update(finalUpdateData)
          .eq('id', post.id);

        if (updateError) throw updateError;

        await logOptimization(supabaseClient, 'blog', post.id, {
          status: 'success',
          original_title: post.title,
          optimized_title: finalUpdateData.title || post.title,
          original_content: post.content,
          optimized_content: 'content' in finalUpdateData ? finalUpdateData.content : post.content,
          original_meta_description: post.excerpt,
          optimized_meta_description: finalUpdateData.meta_description,
          original_tags: post.tags,
          optimized_tags: updateData.seo_tags,
          gemini_response: optimizedData,
          processing_time_ms: Date.now() - startTime,
          optimization_type: trigger
        });

        console.log(`Successfully optimized blog post: ${post.title}`);
        processedCount++;
        successCount++;

        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Error processing blog post ${post.id}:`, errorMessage);

        await logOptimization(supabaseClient, 'blog', post.id, {
          status: 'failed',
          original_title: post.title,
          error_message: errorMessage,
          processing_time_ms: Date.now() - startTime,
          optimization_type: trigger
        });
      }
    }

    // Process trek packages (similar structure to blog posts)
    // ... Trek package optimization logic here ...

    return new Response(
      JSON.stringify({
        message: 'SEO optimization completed',
        processed: processedCount,
        successful: successCount,
        total: blogPosts.length + trekPackages.length,
        trigger
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('SEO optimization error:', error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error',
        suggestions: [],
        score: 0
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

interface LogOptimizationParams {
  status: string;
  original_title: string;
  optimized_title?: string;
  original_content?: string;
  optimized_content?: string;
  original_meta_description?: string;
  optimized_meta_description?: string;
  original_tags?: string[];
  optimized_tags?: string[];
  gemini_response?: Record<string, unknown>;
  error_message?: string;
  processing_time_ms: number;
  optimization_type: string;
}

async function logOptimization(
  supabaseClient: ReturnType<typeof createClient<Database>>,
  contentType: 'blog' | 'trek',
  contentId: string,
  params: LogOptimizationParams
): Promise<void> {
  try {
    // Only log blog post optimizations for now since the table structure is for blog_post_id
    if (contentType === 'blog') {
      await supabaseClient.from('seo_optimization_logs').insert({
        blog_post_id: contentId,
        original_title: params.original_title,
        optimized_title: params.optimized_title || null,
        original_content: params.original_content || '',
        optimized_content: params.optimized_content || null,
        original_meta_description: params.original_meta_description || null,
        optimized_meta_description: params.optimized_meta_description || null,
        original_tags: params.original_tags || [],
        optimized_tags: params.optimized_tags || [],
        optimization_type: params.optimization_type,
        gemini_response: params.gemini_response || null,
        status: params.status,
        error_message: params.error_message || null,
        processing_time_ms: params.processing_time_ms
      });
    }
  } catch (error) {
    console.error('Error logging optimization:', error);
  }
}
