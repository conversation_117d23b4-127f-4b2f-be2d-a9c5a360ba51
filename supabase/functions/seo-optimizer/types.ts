export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  tags: string[];
  published: boolean;
  optimized_title: string | null;
  meta_description: string | null;
  seo_tags: string[] | null;
  last_optimized_at: string | null;
  optimization_count: number | null;
  created_at: string;
  updated_at: string;
}

export interface TrekPackage {
  id: string;
  name: string;
  slug: string;
  region: string;
  short_description: string;
  long_description: string;
  minimum_days: number;
  duration_variations: {
    variations: Array<{
      days: number;
      price_usd: number;
      description?: string;
      itinerary: Array<{
        day: number;
        title: string;
        description: string;
        location?: string;
      }>;
    }>;
  };
  seo_tags?: string[];
  last_optimized_at?: string;
  created_at: string;
  updated_at: string;
}

export interface OptimizationLog {
  id: string;
  content_type: 'blog' | 'trek';
  content_id: string;
  original_title: string;
  optimized_title: string | null;
  original_content: string;
  optimized_content: string | null;
  original_meta_description: string | null;
  optimized_meta_description: string | null;
  original_tags: string[];
  optimized_tags: string[];
  optimization_type: string;
  gemini_response: Record<string, unknown> | null;
  status: string;
  error_message: string | null;
  processing_time_ms: number;
  created_at: string;
}

export interface ContentCalendar {
  id: string;
  suggested_date: string;
  content_type: 'blog' | 'trek';
  topic: string;
  keywords: string[];
  seasonality_score: number;
  predicted_engagement: number;
  status: 'suggested' | 'approved' | 'published';
  ai_reasoning: string | null;
  created_at: string;
  updated_at: string;
}

export interface ImageSEO {
  id: string;
  image_url: string;
  alt_text: string | null;
  suggested_filename: string | null;
  optimization_status: 'pending' | 'optimized';
  compression_needed: boolean;
  content_type: 'blog' | 'trek';
  content_id: string;
  vision_api_tags: string[];
  created_at: string;
  updated_at: string;
}

export interface SocialSEO {
  id: string;
  content_id: string;
  platform: 'facebook' | 'instagram' | 'twitter';
  preview_text: string | null;
  suggested_hashtags: string[];
  og_tags: Record<string, string>;
  engagement_metrics: {
    shares: number;
    likes: number;
    clicks: number;
  };
  seo_impact_score: number;
  created_at: string;
  updated_at: string;
}

export interface SchemaRegistry {
  id: string;
  page_url: string;
  schema_type: 'TrekPackage' | 'BlogPosting' | 'FAQPage' | 'LocalBusiness';
  schema_data: Record<string, unknown>;
  last_validated: string | null;
  validation_errors: string[];
  auto_update: boolean;
  created_at: string;
  updated_at: string;
}

export interface ContentFreshness {
  id: string;
  content_id: string;
  content_type: 'blog' | 'trek';
  last_updated: string;
  freshness_score: number;
  update_priority: 'low' | 'medium' | 'high';
  suggested_updates: Array<{
    section: string;
    reason: string;
    suggestion: string;
  }>;
  seasonal_relevance: boolean;
  next_review_date: string | null;
  created_at: string;
  updated_at: string;
}

/** Payload sent for real-time analysis from the editor */
export interface RealTimeAnalysisPayload {
  content: string;
  title: string;
  excerpt?: string;
  tags: string[];
  blogId?: string;
}

/** Suggestion returned to the editor */
export interface SEOSuggestion {
  type: 'title' | 'meta' | 'content' | 'keyword';
  suggestion: string;
  reason: string;
  impact: 'high' | 'medium' | 'low';
}

/** Response shape for real-time analysis */
export interface RealTimeAnalysisResponse {
  suggestions: SEOSuggestion[];
  score: number;
  metrics?: {
    readability: number;
    keywordDensity: number;
    wordCount: number;
    titleLength: number;
    metaLength: number;
  };
}

export interface Database {
  public: {
    Tables: {
      blog_posts: {
        Row: BlogPost;
        Insert: Omit<BlogPost, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<BlogPost, 'id'>>;
      };
      trek_packages: {
        Row: TrekPackage;
        Insert: Omit<TrekPackage, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<TrekPackage, 'id'>>;
      };
      seo_optimization_logs: {
        Row: OptimizationLog;
        Insert: Omit<OptimizationLog, 'id' | 'created_at'>;
        Update: Partial<Omit<OptimizationLog, 'id'>>;
      };
      content_calendar: {
        Row: ContentCalendar;
        Insert: Omit<ContentCalendar, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<ContentCalendar, 'id'>>;
      };
      image_seo_data: {
        Row: ImageSEO;
        Insert: Omit<ImageSEO, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<ImageSEO, 'id'>>;
      };
      social_seo_metrics: {
        Row: SocialSEO;
        Insert: Omit<SocialSEO, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<SocialSEO, 'id'>>;
      };
      schema_registry: {
        Row: SchemaRegistry;
        Insert: Omit<SchemaRegistry, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<SchemaRegistry, 'id'>>;
      };
      content_freshness: {
        Row: ContentFreshness;
        Insert: Omit<ContentFreshness, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<ContentFreshness, 'id'>>;
      };
    };
  };
}
