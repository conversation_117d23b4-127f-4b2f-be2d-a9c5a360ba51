// supabase/functions/seo-optimizer/utils.ts

export type SuggestionType = 'title' | 'meta' | 'content' | 'keyword';
export type ImpactLevel = 'high' | 'medium' | 'low';

export interface SEOSuggestion {
  type: SuggestionType;
  suggestion: string;
  reason: string;
  impact: ImpactLevel;
}

/** Count words by splitting on whitespace. */
function countWords(text: string): number {
  return (text.trim().match(/\b\w+\b/g) || []).length;
}

/** Count sentences by punctuation. */
function countSentences(text: string): number {
  // naive split on .,!,?
  return (text.match(/[\.!?]+/g) || []).length || 1;
}

/** Very naive syllable counter—counts vowel groups. */
function countSyllables(word: string): number {
  word = word.toLowerCase().replace(/[^a-z]/g, '');
  const groups = word.match(/[aeiouy]{1,2}/g);
  return groups ? groups.length : 1;
}

/** Flesch Reading Ease: 206.835 − 1.015*(words/sentences) − 84.6*(syllables/words) */
export function calculateReadability(text: string): number {
  const words = countWords(text);
  const sentences = countSentences(text);
  const syllables = text
    .split(/\s+/)
    .map(w => countSyllables(w))
    .reduce((a, b) => a + b, 0);
  
  if (words === 0 || sentences === 0) return 0;
  
  const score = 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words);
  return Math.round(Math.max(0, Math.min(100, score)));
}

/** Keyword density = occurrences of any tag ÷ total words × 100 */
export function calculateKeywordDensity(text: string, tags: string[]): number {
  const words = countWords(text);
  if (words === 0 || tags.length === 0) return 0;
  
  const lower = text.toLowerCase();
  let hits = 0;
  
  tags.forEach(tag => {
    const re = new RegExp(`\\b${tag.toLowerCase()}\\b`, 'g');
    hits += (lower.match(re) || []).length;
  });
  
  return parseFloat(((hits / words) * 100).toFixed(2));
}

/** Suggest a title: too long → truncate; too short → recommend adding keywords. */
export function generateTitleSuggestion(title: string, tags: string[]): string | null {
  const len = title.length;
  
  if (len > 60) {
    return title.slice(0, 57).trim() + '...';
  }
  
  if (len < 40 && tags.length) {
    const capitalizedTag = tags[0][0].toUpperCase() + tags[0].slice(1);
    return `${title} | ${capitalizedTag}`;
  }
  
  return null;
}

/** Suggest a meta description: enforce 150–160 chars. */
export function generateMetaSuggestion(excerpt: string): string | null {
  const len = excerpt.length;
  
  if (len > 160) {
    return excerpt.slice(0, 157).trim() + '...';
  }
  
  if (len < 150) {
    return excerpt + ' Learn more about this amazing adventure in Nepal.';
  }
  
  return null;
}

/** Basic content suggestions: break long paragraphs, add headings. */
export function generateContentSuggestions(text: string): Array<{
  type: 'style' | 'structure' | 'keywords';
  suggestion: string;
  position?: { start: number; end: number };
}> {
  const suggestions = [];
  
  // Check for long paragraphs
  const paragraphs = text.split('\n\n');
  paragraphs.forEach((para, i) => {
    if (para.length > 300) {
      suggestions.push({
        type: 'structure' as const,
        suggestion: `Paragraph ${i + 1} is quite long (${para.length} chars). Consider breaking it into smaller sections for better readability.`,
      });
    }
  });
  
  // Check for missing headings
  if (!/^#+\s/m.test(text)) {
    suggestions.push({
      type: 'structure' as const,
      suggestion: 'No markdown headings detected. Use H2/H3 headings (## or ###) to improve content structure and scannability.',
    });
  }
  
  // Check for very short content
  if (countWords(text) < 300) {
    suggestions.push({
      type: 'keywords' as const,
      suggestion: 'Content appears to be quite short. Consider expanding with more detailed information, tips, or insights.',
    });
  }
  
  // Check for lack of lists or bullet points
  if (!/^[\*\-\+]\s/m.test(text) && !/^\d+\.\s/m.test(text)) {
    suggestions.push({
      type: 'structure' as const,
      suggestion: 'Consider adding bullet points or numbered lists to make key information easier to scan.',
    });
  }
  
  return suggestions;
}

/** Pick up to 5 keywords you haven't used yet from the tag list. */
export function getSuggestedKeywords(text: string, tags: string[]): string[] {
  const used = new Set(
    (text.match(/\b\w+\b/g) || []).map(w => w.toLowerCase())
  );
  
  return tags.filter(t => !used.has(t.toLowerCase())).slice(0, 5);
}

/** Combine metrics into a single 0–100 score. */
export function computeOverallScore(
  readability: number,
  keywordDensity: number,
  title: string,
  excerpt: string
): number {
  // Normalize density to a max of 3% (optimal range is 1-3%)
  const normDensity = Math.min(keywordDensity, 3) * (100 / 3);
  
  // Title length optimal at 50–60 characters
  const titleLen = title.length;
  const titleScore = titleLen >= 50 && titleLen <= 60
    ? 100
    : 100 - Math.min(Math.abs(titleLen - 55), 55);
  
  // Meta length optimal 150-160 characters
  const metaLen = excerpt.length;
  const metaScore = metaLen >= 150 && metaLen <= 160
    ? 100
    : 100 - Math.min(Math.abs(metaLen - 155), 155);
  
  // Weighted average: readability is most important
  const score = (
    readability * 0.4 +
    normDensity * 0.2 +
    titleScore * 0.2 +
    metaScore * 0.2
  );
  
  return Math.round(Math.max(0, Math.min(100, score)));
}

/** Generate AI-powered suggestions using content analysis */
export function generateAdvancedSuggestions(
  content: string,
  title: string,
  excerpt: string,
  tags: string[]
): SEOSuggestion[] {
  const suggestions: SEOSuggestion[] = [];
  const wordCount = countWords(content);
  const readability = calculateReadability(content);
  const keywordDensity = calculateKeywordDensity(content, tags);
  
  // Title analysis
  if (title.length > 60) {
    suggestions.push({
      type: 'title',
      suggestion: generateTitleSuggestion(title, tags) || title.slice(0, 57) + '...',
      reason: `Title is ${title.length} characters. Keep it under 60 for better search visibility.`,
      impact: 'high'
    });
  } else if (title.length < 30) {
    suggestions.push({
      type: 'title',
      suggestion: generateTitleSuggestion(title, tags) || `${title} - Complete Guide`,
      reason: 'Title is quite short. Consider adding descriptive keywords to improve SEO.',
      impact: 'medium'
    });
  }
  
  // Meta description analysis
  if (excerpt.length > 160) {
    suggestions.push({
      type: 'meta',
      suggestion: generateMetaSuggestion(excerpt) || excerpt.slice(0, 157) + '...',
      reason: `Meta description is ${excerpt.length} characters. Keep it between 150-160 for optimal display.`,
      impact: 'high'
    });
  } else if (excerpt.length < 120) {
    suggestions.push({
      type: 'meta',
      suggestion: generateMetaSuggestion(excerpt) || excerpt,
      reason: 'Meta description could be longer. Aim for 150-160 characters to maximize search snippet space.',
      impact: 'medium'
    });
  }
  
  // Content length analysis
  if (wordCount < 300) {
    suggestions.push({
      type: 'content',
      suggestion: 'Expand your content with more detailed information, examples, or practical tips.',
      reason: `Content has ${wordCount} words. Aim for at least 300 words for better SEO performance.`,
      impact: 'high'
    });
  }
  
  // Readability analysis
  if (readability < 30) {
    suggestions.push({
      type: 'content',
      suggestion: 'Simplify sentence structure and use shorter paragraphs to improve readability.',
      reason: 'Content readability score is low. Consider using simpler language and shorter sentences.',
      impact: 'medium'
    });
  }
  
  // Keyword density analysis
  if (keywordDensity < 0.5) {
    suggestions.push({
      type: 'keyword',
      suggestion: `Naturally incorporate these keywords: ${tags.slice(0, 3).join(', ')}`,
      reason: 'Keyword density is low. Include your target keywords more naturally throughout the content.',
      impact: 'medium'
    });
  } else if (keywordDensity > 3) {
    suggestions.push({
      type: 'keyword',
      suggestion: 'Reduce keyword repetition and use synonyms or related terms.',
      reason: 'Keyword density is too high, which may appear spammy to search engines.',
      impact: 'high'
    });
  }
  
  // Content structure suggestions
  const contentSuggestions = generateContentSuggestions(content);
  contentSuggestions.forEach(cs => {
    suggestions.push({
      type: 'content',
      suggestion: cs.suggestion,
      reason: cs.type === 'structure' ? 'Improve content structure for better user experience' : 'Enhance content style and presentation',
      impact: cs.type === 'structure' ? 'high' : 'low'
    });
  });
  
  // Unused keywords suggestion
  const unusedKeywords = getSuggestedKeywords(content, tags);
  if (unusedKeywords.length > 0) {
    suggestions.push({
      type: 'keyword',
      suggestion: `Consider using these relevant keywords: ${unusedKeywords.join(', ')}`,
      reason: 'These tags are not yet used in your content and could improve topical relevance.',
      impact: 'low'
    });
  }
  
  return suggestions;
}
