# Supabase Deployment Guide for SEO Features

## 1. Edge Functions to Deploy

Deploy these functions using `supabase functions deploy [function-name]`:

```bash
supabase functions deploy content-freshness-monitor
supabase functions deploy image-seo-optimizer
supabase functions deploy schema-generator
supabase functions deploy content-calendar-generator
```

## 2. Database Tables to Create

Run these SQL migrations:

```sql
-- Content Freshness Table
create table content_freshness (
  id uuid primary key default uuid_generate_v4(),
  content_type text check (content_type in ('blog', 'trek')),
  content_id uuid not null,
  freshness_score decimal not null,
  update_priority text check (update_priority in ('low', 'medium', 'high')),
  suggested_updates jsonb,
  seasonal_relevance boolean default false,
  next_review_date timestamp with time zone,
  last_updated timestamp with time zone default now(),
  created_at timestamp with time zone default now()
);

-- Schema Registry Table
create table schema_registry (
  id uuid primary key default uuid_generate_v4(),
  page_url text not null,
  schema_type text check (schema_type in ('BlogPosting', 'TrekPackage', 'FAQPage', 'LocalBusiness')),
  schema_data jsonb not null,
  last_validated timestamp with time zone,
  validation_errors text[],
  auto_update boolean default true,
  created_at timestamp with time zone default now()
);

-- Image SEO Data Table
create table image_seo_data (
  id uuid primary key default uuid_generate_v4(),
  image_url text not null,
  content_type text check (content_type in ('blog', 'trek')),
  content_id uuid not null,
  alt_text text,
  suggested_filename text,
  vision_api_tags text[],
  optimization_status text check (optimization_status in ('pending', 'optimized')),
  compression_needed boolean default false,
  created_at timestamp with time zone default now()
);

-- Content Calendar Table
create table content_calendar (
  id uuid primary key default uuid_generate_v4(),
  topic text not null,
  keywords text[],
  seasonality_score decimal not null,
  predicted_engagement decimal not null,
  status text check (status in ('suggested', 'approved', 'published')),
  ai_reasoning text,
  suggested_date timestamp with time zone not null,
  created_at timestamp with time zone default now()
);
```

## 3. Environment Variables to Set

Set these in your Supabase project dashboard under Settings > API:

```bash
GEMINI_API_KEY=your_google_gemini_api_key
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 4. Storage Buckets

Create these storage buckets with appropriate policies:

```sql
-- Create buckets
insert into storage.buckets (id, name)
values 
  ('seo-images', 'SEO optimized images'),
  ('schemas', 'JSON-LD schema files');

-- Set bucket policies
create policy "Public SEO images access"
  on storage.objects for select
  using ( bucket_id = 'seo-images' );

create policy "Admin SEO images access"
  on storage.objects for insert
  using ( bucket_id = 'seo-images' AND auth.role() = 'authenticated' );
```

## 5. Row Level Security (RLS) Policies

Apply these RLS policies to protect the tables:

```sql
-- Content Freshness RLS
alter table content_freshness enable row level security;

create policy "Public read content freshness"
  on content_freshness for select
  using ( true );

create policy "Admin write content freshness"
  on content_freshness for insert
  using ( auth.role() = 'authenticated' );

-- Schema Registry RLS
alter table schema_registry enable row level security;

create policy "Public read schemas"
  on schema_registry for select
  using ( true );

create policy "Admin write schemas"
  on schema_registry for insert
  using ( auth.role() = 'authenticated' );

-- Image SEO Data RLS
alter table image_seo_data enable row level security;

create policy "Public read image SEO data"
  on image_seo_data for select
  using ( true );

create policy "Admin write image SEO data"
  on image_seo_data for insert
  using ( auth.role() = 'authenticated' );

-- Content Calendar RLS
alter table content_calendar enable row level security;

create policy "Admin only content calendar"
  on content_calendar for all
  using ( auth.role() = 'authenticated' );
```

## 6. Scheduled Functions (Optional)

Set up these scheduled tasks in the Supabase dashboard:

1. Content Freshness Analysis
   - Function: `content-freshness-monitor`
   - Schedule: Every Sunday at 00:00
   - Payload: `{"mode": "batch"}`

2. Schema Validation
   - Function: `schema-generator`
   - Schedule: Daily at 01:00
   - Payload: `{"mode": "validate_all"}`

3. Content Calendar Suggestions
   - Function: `content-calendar-generator`
   - Schedule: First day of each month at 00:00
   - Payload: `{"mode": "monthly"}`

## 7. Verify Deployment

After deploying, verify the setup:

1. Test Edge Functions:
```bash
curl -i --request POST 'https://xxx.functions.supabase.co/content-freshness-monitor' \
  --header 'Authorization: Bearer YOUR_ANON_KEY' \
  --data '{"mode":"test"}'
```

2. Check Database Tables:
```sql
select * from content_freshness limit 1;
select * from schema_registry limit 1;
select * from image_seo_data limit 1;
select * from content_calendar limit 1;
```

3. Verify Storage Buckets:
```sql
select * from storage.buckets where id in ('seo-images', 'schemas');
```

4. Check RLS Policies:
```sql
select * from pg_policies where tablename = 'content_freshness';
