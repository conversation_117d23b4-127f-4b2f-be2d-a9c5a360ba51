# Production Deployment Guide - Nepal Adventure Platform

This guide provides comprehensive instructions for deploying the Nepal Adventure Platform to production.

## Prerequisites

### Required Services
- **Supabase Project**: Database and authentication
- **Vercel/Netlify**: Frontend hosting
- **Google Gemini API**: AI content generation
- **Domain**: Custom domain for the platform

### Environment Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Gemini AI Configuration
VITE_GEMINI_API_KEY=your_gemini_api_key
GEMINI_API_KEY=your_gemini_api_key

# Optional: Analytics
VITE_GA_TRACKING_ID=your_google_analytics_id
```

## Database Setup

### 1. Supabase Project Configuration

1. **Create Supabase Project**
   ```bash
   # Install Supabase CLI
   npm install -g supabase

   # Login to Supabase
   supabase login

   # Link to your project
   supabase link --project-ref your-project-ref
   ```

2. **Run Migrations**
   ```bash
   # Deploy all migrations
   supabase db push

   # Verify tables are created
   supabase db diff
   ```

3. **Set up Row Level Security (RLS)**
   - All tables have RLS enabled by default
   - Admin users can manage all content
   - Public users can view published content only

### 2. Required Tables
- `trek_packages` - Trek package information
- `blog_posts` - Blog content with SEO optimization
- `booking_inquiries` - Customer booking requests
- `testimonials` - Customer reviews
- `admin_users` - Admin user management
- `seo_data` - SEO optimization data
- `seo_optimization_logs` - SEO operation history

## Frontend Deployment

### Option 1: Vercel Deployment

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm install -g vercel

   # Deploy to Vercel
   vercel --prod
   ```

2. **Environment Variables**
   - Add all environment variables in Vercel dashboard
   - Ensure `VITE_` prefix for client-side variables

3. **Build Configuration**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "installCommand": "npm install"
   }
   ```

### Option 2: Netlify Deployment

1. **Build Settings**
   ```toml
   [build]
   command = "npm run build"
   publish = "dist"

   [build.environment]
   NODE_VERSION = "18"
   ```

2. **Redirects Configuration**
   ```toml
   [[redirects]]
   from = "/*"
   to = "/index.html"
   status = 200
   ```

## API Configuration

### Gemini AI Setup

1. **Get API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create new API key
   - Add to environment variables

2. **Rate Limiting**
   - Default: 60 requests per minute
   - Implement exponential backoff (already included)
   - Monitor usage in Google Cloud Console

### Supabase Edge Functions

1. **Deploy Functions**
   ```bash
   # Deploy SEO optimizer function
   supabase functions deploy seo-optimizer

   # Deploy content freshness monitor
   supabase functions deploy content-freshness-monitor

   # Deploy schema generator
   supabase functions deploy schema-generator
   ```

2. **Function Environment Variables**
   ```bash
   # Set function secrets
   supabase secrets set GEMINI_API_KEY=your_api_key
   supabase secrets set SUPABASE_URL=your_supabase_url
   supabase secrets set SUPABASE_ANON_KEY=your_anon_key
   ```

## Security Configuration

### 1. Authentication Setup

1. **Admin User Creation**
   ```sql
   -- Create admin user
   INSERT INTO admin_users (user_id, email, role)
   VALUES (
     'user-uuid-from-auth-users',
     '<EMAIL>',
     'admin'
   );
   ```

2. **Email Templates**
   - Configure email templates in Supabase Auth
   - Set up custom SMTP (optional)

### 2. CORS Configuration

```javascript
// Supabase CORS settings
{
  "allowedOrigins": [
    "https://yourdomain.com",
    "https://www.yourdomain.com"
  ],
  "allowedMethods": ["GET", "POST", "PUT", "DELETE"],
  "allowedHeaders": ["*"]
}
```

## Performance Optimization

### 1. Image Optimization

1. **Supabase Storage**
   ```sql
   -- Create storage buckets
   INSERT INTO storage.buckets (id, name, public)
   VALUES 
     ('trek-images', 'trek-images', true),
     ('blog-images', 'blog-images', true),
     ('avatars', 'avatars', true);
   ```

2. **CDN Configuration**
   - Enable Supabase CDN for images
   - Configure image transformations

### 2. Database Optimization

1. **Indexes**
   ```sql
   -- Add performance indexes
   CREATE INDEX idx_blog_posts_published ON blog_posts(published, created_at);
   CREATE INDEX idx_trek_packages_featured ON trek_packages(featured, created_at);
   CREATE INDEX idx_booking_inquiries_status ON booking_inquiries(status, created_at);
   ```

2. **Connection Pooling**
   - Use Supabase connection pooling
   - Configure appropriate pool size

## Monitoring and Analytics

### 1. Error Monitoring

1. **Sentry Integration** (Optional)
   ```bash
   npm install @sentry/react @sentry/vite-plugin
   ```

2. **Custom Error Logging**
   - Errors are logged to console
   - Consider external logging service

### 2. Performance Monitoring

1. **Google Analytics**
   - Add GA tracking ID to environment
   - Configure custom events

2. **Supabase Analytics**
   - Monitor database performance
   - Track API usage

## SEO Configuration

### 1. Meta Tags

1. **Dynamic Meta Tags**
   - Implemented for all pages
   - Optimized for Nepal trekking keywords

2. **Structured Data**
   - JSON-LD schema for trek packages
   - Blog post structured data

### 2. Sitemap Generation

1. **Automatic Sitemap**
   ```javascript
   // Generate sitemap
   import { generateSitemap } from '@/utils/sitemapGenerator';
   const sitemap = generateSitemap('https://yourdomain.com');
   ```

2. **Robots.txt**
   ```
   User-agent: *
   Allow: /
   Disallow: /admin/
   Sitemap: https://yourdomain.com/sitemap.xml
   ```

## Backup and Recovery

### 1. Database Backups

1. **Automated Backups**
   - Supabase provides daily backups
   - Configure retention period

2. **Manual Backups**
   ```bash
   # Export database
   supabase db dump > backup.sql

   # Import database
   supabase db reset --db-url postgresql://...
   ```

### 2. Content Backups

1. **Storage Backups**
   - Regular backup of uploaded images
   - Consider external backup service

## Deployment Checklist

### Pre-Deployment
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] Admin users created
- [ ] API keys tested
- [ ] Build process verified

### Post-Deployment
- [ ] SSL certificate configured
- [ ] Custom domain connected
- [ ] Analytics tracking verified
- [ ] Error monitoring active
- [ ] Performance metrics baseline established

### Testing
- [ ] Admin login functionality
- [ ] Content creation and editing
- [ ] SEO optimization working
- [ ] AI content generation functional
- [ ] Booking form submissions
- [ ] Mobile responsiveness

## Maintenance

### Regular Tasks
- Monitor API usage and costs
- Review and optimize database performance
- Update content regularly
- Check for security updates
- Monitor error rates and performance

### Monthly Tasks
- Review analytics data
- Optimize SEO performance
- Update trek package information
- Review and respond to bookings
- Backup critical data

## Troubleshooting

### Common Issues

1. **Gemini API Errors**
   - Check API key validity
   - Verify rate limits
   - Review error logs

2. **Database Connection Issues**
   - Check Supabase project status
   - Verify connection strings
   - Review RLS policies

3. **Build Failures**
   - Check environment variables
   - Verify dependencies
   - Review build logs

### Support Resources
- [Supabase Documentation](https://supabase.com/docs)
- [Vite Documentation](https://vitejs.dev/guide/)
- [React Documentation](https://react.dev/)
- [Gemini API Documentation](https://ai.google.dev/docs)

## Performance Targets

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### API Performance
- **Database Queries**: < 200ms average
- **Gemini API Calls**: < 5s average
- **Page Load Time**: < 3s

### Availability
- **Uptime Target**: 99.9%
- **Error Rate**: < 0.1%
- **Response Time**: < 500ms (95th percentile)
