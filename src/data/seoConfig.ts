// SEO Configuration for all pages
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  structuredData?: object;
}

export const seoConfigs: Record<string, SEOConfig> = {
  home: {
    title: "Nepal Trekking Adventures | Expert Guided Himalayan Treks",
    description: "Discover authentic Nepal trekking adventures with expert local guides. Everest Base Camp, Annapurna Circuit & more. Book your Himalayan journey today!",
    keywords: [
      "nepal trekking",
      "himalayan adventures", 
      "everest base camp",
      "annapurna circuit",
      "sherpa guides",
      "mountain trekking",
      "nepal tours",
      "adventure travel nepal"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "TravelAgency",
      "name": "TrekNepalX",
      "description": "Premier trekking and expedition company in Nepal specializing in authentic Himalayan adventures with expert local guides",
      "url": "https://treknepalx.com",
      "telephone": "+977-1-4444444",
      "email": "<EMAIL>",
      "foundingDate": "2010",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Thamel Marg, Opposite Kathmandu Guest House",
        "addressLocality": "Kathmandu",
        "addressRegion": "Bagmati Province",
        "addressCountry": "Nepal",
        "postalCode": "44600"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": 27.7172,
        "longitude": 85.3240
      },
      "openingHours": "Mo-Fr 09:00-18:00, Sa 09:00-16:00",
      "priceRange": "$500-$5000",
      "serviceType": [
        "Trekking Tours",
        "Mountain Expeditions", 
        "Cultural Tours",
        "Adventure Travel"
      ],
      "sameAs": [
        "https://facebook.com/treknepalx",
        "https://instagram.com/treknepalx",
        "https://twitter.com/treknepalx"
      ],
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "247",
        "bestRating": "5",
        "worstRating": "1"
      }
    }
  },

  treks: {
    title: "Best Nepal Treks 2025 | Everest, Annapurna & Langtang Tours",
    description: "Explore Nepal's best trekking routes: Everest Base Camp, Annapurna Circuit, Langtang Valley. Expert guides, small groups, authentic experiences. Book now!",
    keywords: [
      "nepal treks",
      "everest base camp trek",
      "annapurna circuit",
      "langtang valley",
      "himalayan trekking",
      "mountain expeditions",
      "guided treks nepal",
      "trekking packages"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "name": "Nepal Trekking Packages",
      "description": "Complete list of trekking packages offered by TrekNepalX",
      "numberOfItems": 8,
      "itemListElement": [
        {
          "@type": "TouristTrip",
          "position": 1,
          "name": "Everest Base Camp Trek",
          "description": "14-day guided trek to Everest Base Camp",
          "provider": {
            "@type": "TravelAgency",
            "name": "TrekNepalX"
          }
        },
        {
          "@type": "TouristTrip", 
          "position": 2,
          "name": "Annapurna Circuit Trek",
          "description": "16-day complete Annapurna Circuit with Thorong La Pass",
          "provider": {
            "@type": "TravelAgency",
            "name": "TrekNepalX"
          }
        }
      ]
    }
  },

  about: {
    title: "About TrekNepalX | Local Nepal Trekking Company Since 2010",
    description: "Meet TrekNepalX - Nepal's trusted local trekking company. Expert Sherpa guides, sustainable tourism, and authentic Himalayan experiences since 2010.",
    keywords: [
      "nepal trekking company",
      "sherpa guides",
      "sustainable tourism",
      "local expertise",
      "mountain guides",
      "responsible travel",
      "community tourism",
      "himalayan culture"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "AboutPage",
      "mainEntity": {
        "@type": "TravelAgency",
        "name": "TrekNepalX",
        "foundingDate": "2010",
        "founder": {
          "@type": "Person",
          "name": "Pemba Sherpa"
        },
        "numberOfEmployees": "25",
        "description": "Local trekking company founded by experienced Sherpa guides"
      }
    }
  },

  blog: {
    title: "Nepal Trekking Blog | Expert Tips & Adventure Stories",
    description: "Get expert Nepal trekking tips, route guides, and adventure stories from our experienced guides. Essential reading for your Himalayan adventure planning.",
    keywords: [
      "nepal trekking blog",
      "trekking tips",
      "himalayan adventures",
      "trek planning",
      "mountain climbing advice",
      "nepal travel guide",
      "trekking gear",
      "altitude sickness"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Blog",
      "name": "TrekNepalX Blog",
      "description": "Expert trekking advice and adventure stories from Nepal",
      "publisher": {
        "@type": "Organization",
        "name": "TrekNepalX"
      },
      "blogPost": []
    }
  },

  contact: {
    title: "Contact TrekNepalX | Plan Your Nepal Trekking Adventure",
    description: "Contact TrekNepalX for personalized Nepal trekking advice. Based in Kathmandu with 24/7 support. Free consultation for your Himalayan adventure planning.",
    keywords: [
      "contact nepal trekking",
      "trek planning",
      "kathmandu office",
      "trekking consultation",
      "nepal travel agency",
      "trek booking",
      "adventure planning",
      "himalayan expeditions"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "mainEntity": {
        "@type": "LocalBusiness",
        "name": "TrekNepalX",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Thamel Marg",
          "addressLocality": "Kathmandu",
          "addressCountry": "Nepal",
          "postalCode": "44600"
        },
        "telephone": "+977-1-4444444",
        "email": "<EMAIL>",
        "openingHours": [
          "Mo-Fr 09:00-18:00",
          "Sa 09:00-16:00"
        ]
      }
    }
  },

  faq: {
    title: "Nepal Trekking FAQ | Common Questions About Himalayan Treks",
    description: "Get answers to common Nepal trekking questions. Permits, gear, fitness, altitude, weather - everything you need to know for your Himalayan adventure.",
    keywords: [
      "nepal trekking faq",
      "trekking questions",
      "himalayan trek guide",
      "trek preparation",
      "trekking permits",
      "altitude sickness",
      "trek safety",
      "mountain climbing tips"
    ],
    structuredData: {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What permits do I need for trekking in Nepal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You need TIMS (Trekkers' Information Management System) card and National Park entry permits for most treks in Nepal."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best time to trek in Nepal?",
          "acceptedAnswer": {
            "@type": "Answer", 
            "text": "The best times are pre-monsoon (March-May) and post-monsoon (September-November) seasons for clear mountain views and stable weather."
          }
        }
      ]
    }
  }
};

// Trek-specific SEO configurations
export const getTrekSEO = (trekName: string, region: string, duration: number, price: number): SEOConfig => ({
  title: `${trekName} | ${duration} Days Nepal Trek | TrekNepalX`,
  description: `Experience the ${trekName} in ${region} with expert guides. ${duration}-day adventure starting from $${price}. Book your authentic Himalayan trek today!`,
  keywords: [
    trekName.toLowerCase().replace(/\s+/g, ' '),
    `${region.toLowerCase()} trek`,
    "nepal trekking",
    "himalayan adventure",
    "guided trek",
    "mountain expedition"
  ],
  structuredData: {
    "@context": "https://schema.org",
    "@type": "TouristTrip",
    "name": trekName,
    "description": `${duration}-day guided trek in ${region}, Nepal`,
    "touristType": "Adventure Travelers",
    "provider": {
      "@type": "TravelAgency",
      "name": "TrekNepalX"
    },
    "offers": {
      "@type": "Offer",
      "price": price.toString(),
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "duration": `P${duration}D`
  }
});

// Blog post SEO configuration
export const getBlogSEO = (title: string, excerpt: string, tags: string[], publishDate: string): SEOConfig => ({
  title: `${title} | TrekNepalX Blog`,
  description: excerpt,
  keywords: [
    ...tags,
    "nepal trekking",
    "himalayan adventures",
    "trek guide",
    "mountain climbing"
  ],
  structuredData: {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": title,
    "description": excerpt,
    "datePublished": publishDate,
    "author": {
      "@type": "Organization",
      "name": "TrekNepalX"
    },
    "publisher": {
      "@type": "Organization",
      "name": "TrekNepalX",
      "logo": {
        "@type": "ImageObject",
        "url": "https://treknepalx.com/logo.png"
      }
    },
    "keywords": tags.join(", ")
  }
});
