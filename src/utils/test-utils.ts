import { vi } from 'vitest';
import { supabase, callFunction } from '@/integrations/supabase/client';
import type { ContentFreshnessData, SchemaRegistryEntry, ContentSuggestion } from '@/types/seo';

export const mockSupabaseFunction = <T>(
  functionName: string,
  mockResponse: T
) => {
  // Mock both the direct supabase.functions.invoke and the callFunction wrapper
  vi.spyOn(supabase.functions, 'invoke').mockImplementation(
    async (name) => {
      if (name === functionName) {
        return { data: mockResponse, error: null };
      }
      return { data: null, error: new Error(`Function ${name} not found`) };
    }
  );
  
  vi.mocked(callFunction).mockImplementation(async (name) => {
    if (name === functionName) {
      return mockResponse;
    }
    throw new Error(`Function ${name} not found`);
  });
};

export const mockSupabaseQuery = <T>(
  table: string,
  mockData: T
) => {
  const mockSelect = vi.fn().mockResolvedValue({ data: mockData, error: null });
  const mockFilter = vi.fn().mockReturnValue({ select: mockSelect });
  const mockMatch = vi.fn().mockReturnValue({ select: mockSelect });
  const mockUpdate = vi.fn().mockResolvedValue({ data: mockData, error: null });

  vi.spyOn(supabase, 'from').mockReturnValue({
    select: mockSelect,
    insert: mockFilter,
    update: mockUpdate,
    delete: mockFilter,
    eq: mockFilter,
    neq: mockFilter,
    gt: mockFilter,
    gte: mockFilter,
    lt: mockFilter,
    lte: mockFilter,
    match: mockMatch,
    order: mockFilter,
  } as unknown as ReturnType<typeof supabase.from>);

  return {
    mockSelect,
    mockFilter,
    mockMatch,
    mockUpdate,
  };
};

export const mockSupabaseStorage = (mockUrl: string) => {
  const mockUpload = vi.fn().mockResolvedValue({
    data: { path: mockUrl },
    error: null,
  });

  const mockGetPublicUrl = vi.fn().mockReturnValue({
    data: { publicUrl: mockUrl },
  });

  vi.spyOn(supabase.storage, 'from').mockReturnValue({
    upload: mockUpload,
    getPublicUrl: mockGetPublicUrl,
  } as unknown as ReturnType<typeof supabase.storage.from>);

  return { mockUpload, mockGetPublicUrl };
};

// Mock data generators
export const generateMockContentFreshnessData = (
  override?: Partial<ContentFreshnessData>
): ContentFreshnessData => ({
  id: '123',
  content_type: 'blog',
  content_id: '456',
  freshness_score: 0.85,
  update_priority: 'medium',
  suggested_updates: [
    {
      section: 'introduction',
      reason: 'Content is outdated',
      suggestion: 'Update with current information'
    }
  ],
  seasonal_relevance: true,
  next_review_date: new Date().toISOString(),
  last_updated: new Date().toISOString(),
  ...override
});

export const generateMockSchemaRegistryEntry = (
  override?: Partial<SchemaRegistryEntry>
): SchemaRegistryEntry => ({
  id: '321',
  page_url: '/blog/example',
  schema_type: 'BlogPosting',
  schema_data: {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: 'Example Blog'
  },
  last_validated: new Date().toISOString(),
  validation_errors: [],
  auto_update: true,
  created_at: new Date().toISOString(),
  ...override
});

export const generateMockContentSuggestion = (
  override?: Partial<ContentSuggestion>
): ContentSuggestion => ({
  id: '654',
  topic: 'Best Time to Trek in Nepal',
  keywords: ['trekking', 'nepal', 'seasons'],
  seasonality_score: 0.8,
  predicted_engagement: 0.75,
  status: 'suggested',
  ai_reasoning: 'High search volume during pre-monsoon season',
  suggested_date: new Date().toISOString(),
  ...override
});
