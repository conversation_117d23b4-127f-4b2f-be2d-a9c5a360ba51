import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeRaw from 'rehype-raw';
import rehypeStringify from 'rehype-stringify';
import { toString } from 'hast-util-to-string';
import * as shiki from 'shiki';
import { Root, Element } from 'hast';

let highlighter: shiki.Highlighter | null = null;

export async function initializeHighlighter() {
  if (!highlighter) {
    highlighter = await shiki.createHighlighter({
      themes: ['github-dark'],
      langs: ['typescript', 'javascript', 'jsx', 'tsx', 'json', 'bash', 'markdown', 'css', 'html']
    });
  }
}

function highlightCode(code: string, language: string): string {
  if (!highlighter) {
    console.warn('Highlighter not initialized');
    return code;
  }

  try {
    return highlighter.codeToHtml(code, {
      lang: language,
      theme: 'github-dark'
    });
  } catch (error) {
    console.error('Error highlighting code:', error);
    return code;
  }
}

interface HtmlNode {
  type: string;
  tagName?: string;
  properties?: {
    className?: string[];
  };
  children?: HtmlNode[];
  value?: string;
}

export async function processMarkdown(content: string): Promise<string> {
  // Initialize highlighter if not done yet
  if (!highlighter) {
    await initializeHighlighter();
  }

  const result = await unified()
    .use(remarkParse)
    .use(remarkGfm)
    .use(remarkRehype, { allowDangerousHtml: true })
    .use(rehypeRaw)
    .use(() => (tree: Root) => {
      // Find and highlight code blocks
      const visit = (node: HtmlNode) => {
        if (node.tagName === 'pre' && node.children?.[0]?.tagName === 'code') {
          const codeNode = node.children[0];
          const language = codeNode.properties?.className?.[0]?.replace('language-', '') || 'text';
          const code = codeNode.children?.[0]?.value || '';
          node.properties = node.properties || {};
          node.properties.className = ['shiki'];
          node.children = [{
            type: 'raw',
            value: highlightCode(code, language)
          }];
        }
        if (node.children) {
          node.children.forEach(visit);
        }
      };
      visit(tree as unknown as HtmlNode);
      return tree;
    })
    .use(rehypeStringify)
    .process(content);

  return result.toString();
}

// Helper function to extract metadata from markdown
export function extractMetadata(content: string) {
  const metadataRegex = /^---\s*\n([\s\S]*?)\n---/;
  const match = content.match(metadataRegex);

  if (!match) return { content, metadata: {} };

  const metadataStr = match[1];
  const metadata: Record<string, string> = {};
  const cleanContent = content.replace(match[0], '').trim();

  metadataStr.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split(':');
    if (key && valueParts.length) {
      metadata[key.trim()] = valueParts.join(':').trim();
    }
  });

  return { content: cleanContent, metadata };
}
