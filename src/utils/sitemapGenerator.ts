// Sitemap generator for SEO optimization
export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export const generateSitemap = (baseUrl: string = 'https://treknepalx.com'): string => {
  const urls: SitemapUrl[] = [
    // Main pages
    {
      loc: `${baseUrl}/`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 1.0
    },
    {
      loc: `${baseUrl}/treks`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 0.9
    },
    {
      loc: `${baseUrl}/blog`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'daily',
      priority: 0.8
    },
    {
      loc: `${baseUrl}/about`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: 0.7
    },
    {
      loc: `${baseUrl}/contact`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      loc: `${baseUrl}/faq`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: 0.7
    }
  ];

  // Add popular trek routes (these would be dynamically generated from database)
  const popularTreks = [
    'everest-base-camp-trek',
    'annapurna-circuit-trek',
    'langtang-valley-trek',
    'manaslu-circuit-trek',
    'gokyo-lakes-trek',
    'annapurna-base-camp-trek',
    'upper-mustang-trek',
    'island-peak-climbing'
  ];

  popularTreks.forEach(slug => {
    urls.push({
      loc: `${baseUrl}/treks/${slug}`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: 0.8
    });
  });

  // Add sample blog posts (these would be dynamically generated from database)
  const sampleBlogPosts = [
    'complete-guide-nepal-trekking-beginners',
    'best-time-annapurna-circuit-beginners',
    'everest-base-camp-difficulty-fitness-guide',
    'nepal-trekking-permits-2025-guide',
    'altitude-sickness-prevention-treatment',
    'solo-female-trekking-nepal-safety-tips',
    'nepal-trekking-packing-list-gear-guide',
    'sherpa-culture-traditions-guide',
    'teahouse-trekking-nepal-complete-guide',
    'winter-trekking-nepal-best-routes'
  ];

  sampleBlogPosts.forEach(slug => {
    urls.push({
      loc: `${baseUrl}/blog/${slug}`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: 0.6
    });
  });

  // Generate XML
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n';
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  const urlsetClose = '</urlset>';

  const urlEntries = urls.map(url => {
    let entry = '  <url>\n';
    entry += `    <loc>${url.loc}</loc>\n`;
    if (url.lastmod) entry += `    <lastmod>${url.lastmod}</lastmod>\n`;
    if (url.changefreq) entry += `    <changefreq>${url.changefreq}</changefreq>\n`;
    if (url.priority) entry += `    <priority>${url.priority}</priority>\n`;
    entry += '  </url>\n';
    return entry;
  }).join('');

  return xmlHeader + urlsetOpen + urlEntries + urlsetClose;
};

// Generate robots.txt content
export const generateRobotsTxt = (baseUrl: string = 'https://treknepalx.com'): string => {
  return `User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: facebookexternalhit
Allow: /

User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/

Sitemap: ${baseUrl}/sitemap.xml`;
};

// SEO-friendly URL slug generator
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Generate breadcrumb schema
export const generateBreadcrumbSchema = (breadcrumbs: Array<{name: string, url: string}>) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

// Generate FAQ schema for specific pages
export const generateFAQSchema = (faqs: Array<{question: string, answer: string}>) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
};
