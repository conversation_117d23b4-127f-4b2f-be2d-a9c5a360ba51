export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  cover_image: string | null;
  published: boolean;
  tags: string[];
  created_at: string;
  updated_at: string;
  author_id: string;
  optimization_count?: number;
  last_optimized_at?: string | null;
}

export interface BlogDraft {
  id: string;
  blog_id: string;
  user_id: string;
  title: string;
  content: string;
  excerpt: string;
  cover_image: string | null;
  tags: string[];
  last_saved_at: string;
}

export interface SEOAnalysis {
  blog_id: string;
  score: number;
  suggestions: {
    type: 'title' | 'meta' | 'keyword';
    suggestion: string;
    reason: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  created_at: string;
}

export interface SEOLog {
  id: string;
  blog_id: string;
  action: string;
  details: Record<string, string | number | boolean | null>;
  created_at: string;
}
