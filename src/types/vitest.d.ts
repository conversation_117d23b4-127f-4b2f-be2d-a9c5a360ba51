/// <reference lib="DOM" />
/// <reference lib="DOM.Iterable" />
import { MockInstance } from 'vitest';
import '@testing-library/jest-dom';

interface CustomMatchers<T = unknown> {
  /**
   * Check if a mock was called with arguments that partially match the expected arguments
   */
  toHaveBeenCalledWithMatch: (...args: unknown[]) => boolean;
}

declare module 'vitest' {
  interface Assertion extends CustomMatchers<void> {
    // Add other custom matchers here
  }
}

// Add types for global mocks
declare global {
  interface Window {
    matchMedia: MockInstance;
  }
  
  var ResizeObserver: {
    prototype: ResizeObserver;
    new(callback: ResizeObserverCallback): ResizeObserver;
  };
}

// Re-export the extended matchers
declare module '@testing-library/jest-dom' {
  export interface Matchers<R = void, T = object> extends CustomMatchers<R> {
    // Existing jest-dom matchers are already included
  }
}

// For TypeScript's type checking
export {};
