export interface SEOAnalysisResponse {
  readability: number;
  keywordDensity: number;
  keywordsFound: boolean;
  suggestedKeywords: string[];
  titleSuggestion?: string;
  metaSuggestion?: string;
  contentSuggestions?: Array<{
    type: 'style' | 'structure' | 'keywords';
    suggestion: string;
    position?: {
      start: number;
      end: number;
    };
  }>;
}

export interface SEOSuggestion {
  type: 'title' | 'meta' | 'content' | 'keyword';
  suggestion: string;
  reason: string;
  impact: 'high' | 'medium' | 'low';
}

export interface SEOMetrics {
  score: number;
  titleLength: number;
  metaLength: number;
  readabilityScore: number;
  keywordDensity: number;
  suggestedKeywords: string[];
}

export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  onAutosave: () => void;
  initialContent?: string;
  placeholder?: string;
  readOnly?: boolean;
  minHeight?: string;
}

export interface BlogDraft {
  id: string;
  blog_id: string;
  user_id: string;
  title: string;
  content: string;
  excerpt?: string;
  cover_image?: string;
  tags: string[];
  seo_data: Record<string, unknown>;
  last_saved_at: string;
  created_at: string;
  updated_at: string;
}

export interface BlogVersion {
  id: string;
  blog_id: string;
  version_number: number;
  title: string;
  content: string;
  excerpt?: string;
  cover_image?: string;
  tags: string[];
  created_by: string;
  created_at: string;
}

export interface SEOABTest {
  id: string;
  blog_id: string;
  variant_a: string;
  variant_b: string;
  test_type: 'title' | 'meta_description' | 'excerpt';
  start_date: string;
  end_date?: string;
  winner_variant?: 'a' | 'b';
  metrics: {
    a: { views: number; clicks: number; conversions: number };
    b: { views: number; clicks: number; conversions: number };
  };
  status: 'running' | 'completed' | 'stopped';
}

// Core SEO types for the enhanced optimizer
export interface SEOOptimizationRequest {
  content: string;
  title: string;
  excerpt?: string;
  tags?: string[];
  targetKeywords?: string[];
  contentType: 'blog' | 'trek' | 'page';
  priority?: number;
}

export interface SEOOptimizationResponse {
  optimizedTitle: string;
  optimizedMetaDescription: string;
  focusKeywords: string[];
  contentSuggestions: Array<{
    section: string;
    suggestion: string;
    reason: string;
  }>;
  technicalRecommendations: string[];
  optimizationConfidence: number;
  estimatedImpact: 'low' | 'medium' | 'high';
  processingTime: number;
  version: string;
}
