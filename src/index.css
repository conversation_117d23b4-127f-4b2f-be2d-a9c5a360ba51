@import './styles/prose.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Code block scrollbar styles */
.prose pre::-webkit-scrollbar {
  height: 8px;
}

.prose pre::-webkit-scrollbar-track {
  @apply bg-muted;
  border-radius: 4px;
}

.prose pre::-webkit-scrollbar-thumb {
  @apply bg-primary/20 hover:bg-primary/30;
  border-radius: 4px;
}

/* Markdown editor styles */
.rich-text-editor {
  @apply relative min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm;
}

.rich-text-editor .editor-toolbar {
  @apply sticky top-0 z-10 flex flex-wrap items-center gap-1 border-b bg-background/95 py-2 backdrop-blur supports-[backdrop-filter]:bg-background/60;
}

.rich-text-editor .editor-content {
  @apply prose dark:prose-invert min-h-[150px] py-4 focus:outline-none;
}

/* Preview mode */
.preview-content {
  @apply prose dark:prose-invert max-w-none p-4;
}

/* Auto-save indicator */
.autosave-indicator {
  @apply text-sm text-muted-foreground;
}

/* Image placeholder */
.image-placeholder {
  @apply flex h-32 items-center justify-center rounded border-2 border-dashed bg-muted/30;
}

/* Table styles */
.prose table {
  @apply w-full overflow-hidden rounded-lg border;
}

.prose th {
  @apply bg-muted px-4 py-2 text-left font-medium;
}

.prose td {
  @apply border-t px-4 py-2;
}
