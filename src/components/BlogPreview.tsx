import { useEffect, useState } from 'react';
import { useMarkdown } from '@/hooks/use-markdown';
import { Skeleton } from '@/components/ui/skeleton';

interface BlogPreviewProps {
  content: string;
  className?: string;
}

export default function BlogPreview({ content, className }: BlogPreviewProps) {
  const [html, setHtml] = useState('');
  const { process, isProcessing } = useMarkdown();

  useEffect(() => {
    let mounted = true;

    const processContent = async () => {
      try {
        const processed = await process(content);
        if (mounted) {
          setHtml(processed);
        }
      } catch (error) {
        console.error('Error processing markdown:', error);
      }
    };

    processContent();

    return () => {
      mounted = false;
    };
  }, [content, process]);

  if (isProcessing) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-4 w-5/6" />
      </div>
    );
  }

  return (
    <article 
      className={className}
      dangerouslySetInnerHTML={{ __html: html }} 
    />
  );
}
