import React, { useState, useEffect } from 'react';
import { Clock, Users, TrendingUp, Star, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { TrekPackage } from '@/types/database';
import BookingModal from './BookingModal';
import { Link } from 'react-router-dom';

const FeaturedTreks = () => {
  const [treks, setTreks] = useState<TrekPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTrek, setSelectedTrek] = useState<string>('');
  const [isBookingOpen, setIsBookingOpen] = useState(false);

  useEffect(() => {
    fetchFeaturedTreks();
  }, []);

  const fetchFeaturedTreks = async () => {
    try {
      const { data, error } = await supabase
        .from('trek_packages')
        .select('*')
        .eq('featured', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Transform the data to match our TrekPackage interface
      const transformedTreks = data?.map(trek => ({
        ...trek,
        itinerary: Array.isArray(trek.itinerary) ? trek.itinerary : []
      })) as TrekPackage[] || [];
      
      setTreks(transformedTreks);
    } catch (error) {
      console.error('Error fetching treks:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = (trekName: string) => {
    setSelectedTrek(trekName);
    setIsBookingOpen(true);
  };

  if (loading) {
    return (
      <div className="py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading featured treks...</p>
        </div>
      </div>
    );
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Featured Trek Packages
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our most popular adventures, carefully crafted for unforgettable experiences 
            in the world's most spectacular mountain ranges.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {treks.map((trek) => (
            <div key={trek.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="relative h-48 bg-gray-200">
                <img 
                  src={`https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80`}
                  alt={trek.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Featured
                  </span>
                </div>
                {trek.price_usd && (
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                    <span className="text-lg font-bold text-gray-900">${trek.price_usd}</span>
                  </div>
                )}
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-blue-600 font-medium">{trek.region}</span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600 ml-1">4.9</span>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                  {trek.name}
                </h3>

                <p className="text-gray-600 mb-4 line-clamp-2">
                  {trek.short_description}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{trek.duration_days} days</span>
                  </div>
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    <span>{trek.difficulty_level}</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    <span>2-12 people</span>
                  </div>
                </div>

                <div className="flex gap-3">
                  <Link to={`/treks/${trek.slug}`} className="flex-1">
                    <Button variant="outline" className="w-full">
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    onClick={() => handleBookNow(trek.name)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    Book Now
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link to="/treks">
            <Button size="lg" variant="outline" className="px-8">
              View All Trek Packages
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>

      <BookingModal 
        isOpen={isBookingOpen} 
        onClose={() => setIsBookingOpen(false)}
        trekName={selectedTrek}
      />
    </section>
  );
};

export default FeaturedTreks;
