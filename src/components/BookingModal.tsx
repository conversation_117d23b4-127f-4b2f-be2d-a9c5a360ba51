import React, { useState } from 'react';
import { X, Calendar, User, Mail, Phone, MessageSquare, Mountain, Clock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  trekName?: string;
  selectedDuration?: number;
}

const BookingModal = ({ isOpen, onClose, trekName = '', selectedDuration }: BookingModalProps) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    trek_name: trekName,
    preferred_dates: '',
    message: '',
    selected_duration: selectedDuration
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('booking_inquiries')
        .insert([formData]);

      if (error) throw error;

      toast({
        title: "Booking inquiry submitted!",
        description: "We'll contact you soon. Visit our office in Thamel for the best deals!",
      });

      setFormData({
        name: '',
        email: '',
        phone: '',
        trek_name: '',
        preferred_dates: '',
        message: '',
        selected_duration: undefined
      });
      onClose();
    } catch (error) {
      console.error('Error submitting booking:', error);
      toast({
        title: "Error",
        description: "Failed to submit booking inquiry. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Mountain className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-bold text-gray-900" style={{ fontFamily: 'DM Serif Display, serif' }}>
                Book Your Adventure
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name" className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Full Name</span>
              </Label>
              <Input
                id="name"
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="email" className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>Email Address</span>
              </Label>
              <Input
                id="email"
                type="email"
                required
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="phone" className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>Phone Number</span>
              </Label>
              <Input
                id="phone"
                type="tel"
                required
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="mt-1"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="trek_name">
                  Trek Package
                </Label>
                <Input
                  id="trek_name"
                  type="text"
                  required
                  value={formData.trek_name}
                  onChange={(e) => setFormData({ ...formData, trek_name: e.target.value })}
                  className="mt-1"
                  placeholder="e.g., Everest Base Camp Trek"
                />
              </div>
              {selectedDuration && (
                <div>
                  <Label htmlFor="duration" className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Duration</span>
                  </Label>
                  <Input
                    id="duration"
                    type="text"
                    value={`${selectedDuration} days`}
                    readOnly
                    className="mt-1 bg-gray-50"
                  />
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="preferred_dates" className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Preferred Dates</span>
              </Label>
              <Input
                id="preferred_dates"
                type="text"
                value={formData.preferred_dates}
                onChange={(e) => setFormData({ ...formData, preferred_dates: e.target.value })}
                className="mt-1"
                placeholder="e.g., March 2024 or Flexible"
              />
            </div>

            <div>
              <Label htmlFor="message" className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4" />
                <span>Additional Message</span>
              </Label>
              <textarea
                id="message"
                rows={3}
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Any special requirements or questions?"
              />
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>💡 Best Deals:</strong> Visit our office in Thamel, Kathmandu for personalized service and exclusive discounts!
              </p>
            </div>

            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Inquiry'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
