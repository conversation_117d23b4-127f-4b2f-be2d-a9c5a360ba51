
import React from 'react';
import { Users, Award, Heart, Shield } from 'lucide-react';

const AboutSection = () => {
  const features = [
    {
      icon: Users,
      title: "Local Expertise",
      description: "Our experienced Sherpa and local guides know every trail, ensuring your safety and cultural immersion."
    },
    {
      icon: Award,
      title: "15+ Years Experience",
      description: "Over a decade of organizing successful treks with thousands of satisfied adventurers from around the world."
    },
    {
      icon: Heart,
      title: "Personal Touch",
      description: "Visit our office in Thamel for face-to-face planning and get the best deals on your dream adventure."
    },
    {
      icon: Shield,
      title: "Safety First",
      description: "Comprehensive safety protocols, emergency procedures, and insurance coverage for peace of mind."
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Why Choose TrekNepalX?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We believe in authentic experiences, personal connections, and the magic that happens 
            when you explore Nepal with people who call it home.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="text-center group">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-full mb-4 group-hover:bg-blue-700 transition-colors">
                <feature.icon className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
                Our Story
              </h3>
              <p className="text-gray-600 mb-4">
                Founded by passionate mountain enthusiasts in the heart of Kathmandu, TrekNepalX was born from a simple belief: 
                that the best adventures happen when you explore with locals who understand both the mountains and the culture.
              </p>
              <p className="text-gray-600 mb-6">
                We focus on building relationships, not just bookings. That's why we encourage all our trekkers to visit 
                our office in Thamel - because the best adventures start with a conversation over tea.
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 font-medium">
                  📍 Visit us in Thamel, Kathmandu for personalized trip planning and exclusive in-person deals!
                </p>
              </div>
            </div>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=600&h=400&fit=crop" 
                alt="Local guides in Nepal"
                className="rounded-lg shadow-lg w-full h-64 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
