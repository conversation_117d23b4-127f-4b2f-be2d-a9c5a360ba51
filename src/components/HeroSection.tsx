
import React, { useState } from 'react';
import { ArrowR<PERSON>, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import BookingModal from './BookingModal';

const HeroSection = () => {
  const [isBookingOpen, setIsBookingOpen] = useState(false);

  const handleWhatsAppClick = () => {
    const whatsappNumber = "+9779841234567";
    const message = "Hi! I'm interested in trekking packages. Can you help me?";
    const url = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  return (
    <>
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop')`
          }}
        />
        
        {/* Content */}
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Explore the Heart of the
            <span className="block text-orange-400">Himalayas</span>
          </h1>
          
          <p className="text-xl sm:text-2xl mb-8 text-gray-200">
            Custom & Group Treks Across Nepal
          </p>
          
          <p className="text-lg mb-12 text-gray-300 max-w-2xl mx-auto">
            Experience authentic adventures with local guides, discover hidden trails, 
            and create memories that last a lifetime in the world's most spectacular mountains.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={() => setIsBookingOpen(true)}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
            >
              Explore Packages
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            
            <Button
              onClick={handleWhatsAppClick}
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg"
            >
              <MessageCircle className="mr-2 h-5 w-5" />
              Chat on WhatsApp
            </Button>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      <BookingModal 
        isOpen={isBookingOpen} 
        onClose={() => setIsBookingOpen(false)} 
      />
    </>
  );
};

export default HeroSection;
