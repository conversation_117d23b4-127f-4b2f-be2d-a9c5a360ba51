import React, { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Loader2, Upload } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';

interface ImageUploadProps {
  contentType: 'blog' | 'trek';
  contentId: string;
  onUploadComplete?: (imageUrl: string, metadata: {
    alt_text: string;
    tags: string[];
  }) => void;
}

export function ImageUpload({ contentType, contentId, onUploadComplete }: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const handleUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      if (!event.target.files || event.target.files.length === 0) {
        return;
      }

      const file = event.target.files[0];
      const fileExt = file.name.split('.').pop();
      const filePath = `${contentType}/${contentId}/${Math.random()}.${fileExt}`;

      setUploading(true);
      setProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      // Upload to storage
      const { data: imageData, error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      clearInterval(progressInterval);
      setProgress(100);

      if (uploadError) throw uploadError;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      const metadata = {
        alt_text: file.name.split('.')[0], // Use filename as alt text
        tags: []
      };

      toast({
        title: "Success",
        description: "Image uploaded successfully",
      });

      // Notify parent component
      onUploadComplete?.(publicUrl, metadata);

    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  }, [contentType, contentId, onUploadComplete]);

  return (
    <Card className="w-full max-w-md">
      <CardContent className="p-4">
        <div className="space-y-4">
          <input
            type="file"
            id="image"
            className="hidden"
            accept="image/*"
            onChange={handleUpload}
            disabled={uploading}
          />
          
          <Button 
            variant="outline" 
            className="w-full h-32 border-dashed"
            onClick={() => document.getElementById('image')?.click()}
            disabled={uploading}
          >
            {uploading ? (
              <div className="space-y-2 w-full">
                <div className="flex items-center justify-center">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center space-y-2">
                <Upload className="h-8 w-8" />
                <span>Click to upload image</span>
              </div>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
