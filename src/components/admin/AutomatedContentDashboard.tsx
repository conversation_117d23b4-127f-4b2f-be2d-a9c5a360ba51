/**
 * Automated Content Management Dashboard
 * Central control panel for automated content generation and SEO optimization
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { 
  Brain,
  Zap,
  FileText,
  TrendingUp,
  Settings,
  Play,
  Pause,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  Sparkles,
  BarChart3,
  Search,
  PenTool,
  Cog
} from 'lucide-react';
import { automatedContentGenerator } from '@/lib/content/automated-content-generator';
import { backgroundSEOService } from '@/lib/seo/background-seo-service';
import { toast } from '@/hooks/use-toast';

interface ContentGenerationStats {
  totalGenerated: number;
  pendingGeneration: number;
  successRate: number;
  lastGenerated: Date | null;
}

interface SEOOptimizationStats {
  totalOptimized: number;
  pendingOptimization: number;
  averageScore: number;
  lastOptimized: Date | null;
}

export default function AutomatedContentDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentStats, setContentStats] = useState<ContentGenerationStats>({
    totalGenerated: 0,
    pendingGeneration: 0,
    successRate: 0,
    lastGenerated: null
  });
  const [seoStats, setSeoStats] = useState<SEOOptimizationStats>({
    totalOptimized: 0,
    pendingOptimization: 0,
    averageScore: 0,
    lastOptimized: null
  });
  const [queueStatus, setQueueStatus] = useState({
    totalJobs: 0,
    pendingJobs: 0,
    processingJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    isProcessing: false
  });

  // Settings
  const [autoGenerationEnabled, setAutoGenerationEnabled] = useState(false);
  const [autoOptimizationEnabled, setAutoOptimizationEnabled] = useState(true);
  const [generationInterval, setGenerationInterval] = useState(24); // hours
  const [postsPerBatch, setPostsPerBatch] = useState(3);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      // Get SEO queue status
      const status = backgroundSEOService.getQueueStatus();
      setQueueStatus(status);

      // Update stats (in a real implementation, these would come from a database)
      setSeoStats(prev => ({
        ...prev,
        pendingOptimization: status.pendingJobs,
        totalOptimized: status.completedJobs
      }));

      setContentStats(prev => ({
        ...prev,
        pendingGeneration: automatedContentGenerator.isGeneratingContent() ? 1 : 0
      }));
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const handleGenerateContent = async () => {
    if (isGenerating) return;

    setIsGenerating(true);
    try {
      toast({
        title: "🚀 Starting content generation",
        description: `Generating ${postsPerBatch} blog posts automatically...`,
      });

      const generatedIds = await automatedContentGenerator.generateContentBatch(postsPerBatch);
      
      setContentStats(prev => ({
        ...prev,
        totalGenerated: prev.totalGenerated + generatedIds.length,
        lastGenerated: new Date(),
        successRate: 95 // This would be calculated from actual data
      }));

      toast({
        title: "✅ Content generation complete",
        description: `Successfully generated ${generatedIds.length} blog posts.`,
      });
    } catch (error) {
      toast({
        title: "❌ Content generation failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptimizeAllContent = async () => {
    try {
      toast({
        title: "🔄 Starting SEO optimization",
        description: "Queuing all content for SEO optimization...",
      });

      const queuedCount = await backgroundSEOService.optimizeAllContent();
      
      toast({
        title: "✅ SEO optimization queued",
        description: `${queuedCount} items queued for optimization.`,
      });

      loadDashboardData();
    } catch (error) {
      toast({
        title: "❌ SEO optimization failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    }
  };

  const handleAnalyzeContent = async () => {
    try {
      toast({
        title: "📊 Analyzing content",
        description: "Analyzing existing content for optimization opportunities...",
      });

      const analysis = await automatedContentGenerator.analyzeExistingContent();
      
      toast({
        title: "✅ Content analysis complete",
        description: `Found ${analysis.contentGaps.length} content gaps and ${analysis.needsOptimization.length} posts needing optimization.`,
      });
    } catch (error) {
      toast({
        title: "❌ Content analysis failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Automated Content Management</h2>
          <p className="text-muted-foreground">
            AI-powered content generation and SEO optimization for your Nepal adventure platform
          </p>
        </div>
        <div className="flex items-center gap-2">
          {queueStatus.isProcessing && (
            <Badge variant="secondary" className="animate-pulse">
              <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
              Processing
            </Badge>
          )}
          {isGenerating && (
            <Badge variant="secondary" className="animate-pulse">
              <PenTool className="mr-1 h-3 w-3" />
              Generating
            </Badge>
          )}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Generated Posts</p>
                <p className="text-2xl font-bold">{contentStats.totalGenerated}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEO Optimized</p>
                <p className="text-2xl font-bold">{seoStats.totalOptimized}</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Queue Status</p>
                <p className="text-2xl font-bold">{queueStatus.pendingJobs}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{contentStats.successRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content Generation</TabsTrigger>
          <TabsTrigger value="seo">SEO Optimization</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Content Generation Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-generation:</span>
                  {getStatusIcon(autoGenerationEnabled ? 'healthy' : 'warning')}
                  <span className="text-sm">{autoGenerationEnabled ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Last generated:</span>
                  <span className="text-sm text-muted-foreground">
                    {contentStats.lastGenerated ? contentStats.lastGenerated.toLocaleDateString() : 'Never'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Success rate:</span>
                  <span className="text-sm font-medium">{contentStats.successRate}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  SEO Optimization Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-optimization:</span>
                  {getStatusIcon(autoOptimizationEnabled ? 'healthy' : 'warning')}
                  <span className="text-sm">{autoOptimizationEnabled ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Queue status:</span>
                  <span className="text-sm text-muted-foreground">
                    {queueStatus.pendingJobs} pending, {queueStatus.processingJobs} processing
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Average score:</span>
                  <span className="text-sm font-medium">{seoStats.averageScore}/10</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  className="h-20 flex-col gap-2"
                >
                  {isGenerating ? (
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  ) : (
                    <Sparkles className="h-6 w-6" />
                  )}
                  Generate Content
                </Button>

                <Button 
                  onClick={handleOptimizeAllContent}
                  variant="outline"
                  className="h-20 flex-col gap-2"
                >
                  <Target className="h-6 w-6" />
                  Optimize All SEO
                </Button>

                <Button 
                  onClick={handleAnalyzeContent}
                  variant="outline"
                  className="h-20 flex-col gap-2"
                >
                  <BarChart3 className="h-6 w-6" />
                  Analyze Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PenTool className="h-5 w-5" />
                Content Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Button 
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate {postsPerBatch} Blog Posts
                    </>
                  )}
                </Button>
                <div className="text-sm text-muted-foreground">
                  {isGenerating ? 'AI is researching and writing content...' : 'Ready to generate content'}
                </div>
              </div>

              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Generating content...</span>
                    <span>Please wait</span>
                  </div>
                  <Progress value={undefined} className="h-2" />
                </div>
              )}

              <Alert>
                <Brain className="h-4 w-4" />
                <AlertDescription>
                  AI will research trending topics, analyze content gaps, and generate high-quality blog posts 
                  optimized for Nepal adventure tourism keywords.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                SEO Optimization Queue
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{queueStatus.pendingJobs}</div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{queueStatus.processingJobs}</div>
                  <div className="text-sm text-muted-foreground">Processing</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{queueStatus.completedJobs}</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{queueStatus.failedJobs}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleOptimizeAllContent} className="flex-1">
                  <Target className="mr-2 h-4 w-4" />
                  Optimize All Content
                </Button>
                <Button 
                  onClick={() => backgroundSEOService.clearCompletedJobs()}
                  variant="outline"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Clear Completed
                </Button>
              </div>

              {queueStatus.isProcessing && (
                <Alert>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <AlertDescription>
                    SEO optimization is running in the background. Content will be automatically optimized and updated.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Automation Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-generation">Auto Content Generation</Label>
                    <div className="text-sm text-muted-foreground">
                      Automatically generate new blog posts
                    </div>
                  </div>
                  <Switch
                    id="auto-generation"
                    checked={autoGenerationEnabled}
                    onCheckedChange={setAutoGenerationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-seo">Auto SEO Optimization</Label>
                    <div className="text-sm text-muted-foreground">
                      Automatically optimize content for SEO
                    </div>
                  </div>
                  <Switch
                    id="auto-seo"
                    checked={autoOptimizationEnabled}
                    onCheckedChange={setAutoOptimizationEnabled}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="generation-interval">Generation Interval (hours)</Label>
                  <Input
                    id="generation-interval"
                    type="number"
                    value={generationInterval}
                    onChange={(e) => setGenerationInterval(Number(e.target.value))}
                    min="1"
                    max="168"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="posts-per-batch">Posts per Batch</Label>
                  <Input
                    id="posts-per-batch"
                    type="number"
                    value={postsPerBatch}
                    onChange={(e) => setPostsPerBatch(Number(e.target.value))}
                    min="1"
                    max="10"
                  />
                </div>
              </div>

              <Alert>
                <Cog className="h-4 w-4" />
                <AlertDescription>
                  These settings control how the AI automation system operates. Changes take effect immediately.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
