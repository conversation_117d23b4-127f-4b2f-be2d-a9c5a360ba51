import { useState } from 'react';
import { ImageUpload } from '@/components/ImageUpload';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
interface TrekFormImageSectionProps {
  trekId: string;
  onImagesUpdate: (images: Array<{
    url: string;
    alt_text: string;
    tags: string[];
  }>) => void;
}

export function TrekFormImageSection({ trekId, onImagesUpdate }: TrekFormImageSectionProps) {
  const [uploadedImages, setUploadedImages] = useState<Array<{
    url: string;
    alt_text: string;
    tags: string[];
  }>>([]);

  const handleUploadComplete = (imageUrl: string, metadata: {
    alt_text: string;
    tags: string[];
  }) => {
    const newImage = {
      url: imageUrl,
      alt_text: metadata.alt_text,
      tags: metadata.tags
    };

    setUploadedImages(prev => [...prev, newImage]);
    onImagesUpdate([...uploadedImages, newImage]);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Trek Images</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <ImageUpload
              contentType="trek"
              contentId={trekId}
              onUploadComplete={handleUploadComplete}
            />
          </div>

          {uploadedImages.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Uploaded Images</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {uploadedImages.map((image, index) => (
                  <Card key={index} className="overflow-hidden">
                    <img
                      src={image.url}
                      alt={image.alt_text}
                      className="w-full h-48 object-cover"
                    />
                    <CardContent className="p-4">
                      <p className="text-sm text-gray-600 mb-2">{image.alt_text}</p>
                      <div className="flex flex-wrap gap-1">
                        {image.tags.map((tag, i) => (
                          <Badge key={i} variant="secondary">{tag}</Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
