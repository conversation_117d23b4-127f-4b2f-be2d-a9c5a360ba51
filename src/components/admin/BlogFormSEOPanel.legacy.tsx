import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { Info, Check, AlertTriangle } from 'lucide-react';

interface SEOSuggestion {
  type: 'title' | 'meta' | 'keyword';
  suggestion: string;
  reason: string;
  impact: 'high' | 'medium' | 'low';
}

interface BlogFormSEOPanelProps {
  content: string;
  title: string;
  excerpt: string;
  tags: string[];
  blogId?: string;
  onSuggestionApply: (field: 'title' | 'meta' | 'keyword', value: string) => void;
}

export default function BlogFormSEOPanelLegacy({
  content,
  title,
  excerpt,
  tags,
  blogId,
  onSuggestionApply
}: BlogFormSEOPanelProps) {
  const [suggestions, setSuggestions] = useState<SEOSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [seoScore, setSeoScore] = useState<number | null>(null);

  useEffect(() => {
    if (content && title) {
      analyzeSEO();
    }
  }, [content, title, excerpt, tags]);

  const analyzeSEO = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('seo-analyzer', {
        body: {
          content,
          title,
          excerpt,
          tags
        }
      });

      if (error) throw error;

      setSuggestions(data.suggestions || []);
      setSeoScore(data.score || 0);
    } catch (error) {
      console.error('SEO analysis error:', error);
      setSuggestions([]);
      setSeoScore(null);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card className="p-4">
        <div className="space-y-4">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">SEO Analysis (Legacy)</h3>
          {seoScore !== null && (
            <div className={`text-2xl font-bold ${getScoreColor(seoScore)}`}>
              {seoScore}/100
            </div>
          )}
        </div>

        {suggestions.length > 0 ? (
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {suggestions.map((suggestion, index) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {suggestion.impact === 'high' && <AlertTriangle className="h-4 w-4 text-red-500" />}
                      {suggestion.impact === 'medium' && <Info className="h-4 w-4 text-yellow-500" />}
                      {suggestion.impact === 'low' && <Check className="h-4 w-4 text-green-500" />}
                      <span className="font-medium capitalize">{suggestion.type}</span>
                    </div>
                    <Badge className={getImpactColor(suggestion.impact)}>
                      {suggestion.impact}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{suggestion.reason}</p>
                  
                  <div className="flex items-center gap-2">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1">
                      {suggestion.suggestion}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onSuggestionApply(suggestion.type, suggestion.suggestion)}
                    >
                      Apply
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Info className="h-8 w-8 mx-auto mb-2" />
            <p>No SEO suggestions available</p>
            <p className="text-sm">Add content to get SEO recommendations</p>
          </div>
        )}

        <Button 
          onClick={analyzeSEO} 
          disabled={loading || !content || !title}
          className="w-full"
        >
          {loading ? 'Analyzing...' : 'Refresh Analysis'}
        </Button>
      </div>
    </Card>
  );
}
