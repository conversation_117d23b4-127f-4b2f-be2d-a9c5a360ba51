import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Zap, 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  Search,
  Target,
  Globe,
  Activity
} from 'lucide-react';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';

interface SEOMetrics {
  totalOptimizations: number;
  successRate: number;
  averageProcessingTime: number;
  systemHealth: 'healthy' | 'degraded' | 'unhealthy';
  recentOptimizations: Array<{
    id: string;
    title: string;
    confidence: number;
    timestamp: string;
    impact: 'low' | 'medium' | 'high';
  }>;
}

const SEODashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [metrics, setMetrics] = useState<SEOMetrics | null>(null);
  const [loading, setLoading] = useState(false);

  const {
    stats,
    health,
    checkHealth,
    resetStats,
    isHealthy,
    isDegraded,
    isUnhealthy,
    successRate,
    averageProcessingTime
  } = useEnhancedSEOOptimizer();

  useEffect(() => {
    loadMetrics();
    const interval = setInterval(loadMetrics, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadMetrics = async () => {
    setLoading(true);
    try {
      await checkHealth();
      
      // Mock recent optimizations data - in real app, this would come from your database
      const mockRecentOptimizations = [
        {
          id: '1',
          title: 'Everest Base Camp Trek Guide',
          confidence: 8.5,
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          impact: 'high' as const
        },
        {
          id: '2', 
          title: 'Annapurna Circuit Adventure',
          confidence: 7.2,
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          impact: 'medium' as const
        },
        {
          id: '3',
          title: 'Langtang Valley Trekking',
          confidence: 9.1,
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
          impact: 'high' as const
        }
      ];

      setMetrics({
        totalOptimizations: stats?.totalRequests || 0,
        successRate: successRate || 0,
        averageProcessingTime: averageProcessingTime || 0,
        systemHealth: isHealthy ? 'healthy' : isDegraded ? 'degraded' : 'unhealthy',
        recentOptimizations: mockRecentOptimizations
      });
    } catch (error) {
      console.error('Failed to load SEO metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    await loadMetrics();
  };

  const getHealthIcon = () => {
    if (isHealthy) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (isDegraded) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    return <AlertTriangle className="h-5 w-5 text-red-500" />;
  };

  const getHealthStatus = () => {
    if (isHealthy) return 'System Healthy';
    if (isDegraded) return 'Performance Degraded';
    return 'System Issues';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffMins < 60) {
      return `${diffMins} minutes ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hours ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">SEO Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor and optimize your content's search engine performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          {getHealthIcon()}
          <span className="text-sm font-medium">{getHealthStatus()}</span>
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Optimizations</p>
                <p className="text-2xl font-bold">{metrics?.totalOptimizations || 0}</p>
              </div>
              <Search className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{(metrics?.successRate || 0).toFixed(1)}%</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Processing Time</p>
                <p className="text-2xl font-bold">{(metrics?.averageProcessingTime || 0).toFixed(1)}s</p>
              </div>
              <Activity className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Status</p>
                <p className="text-2xl font-bold capitalize">{metrics?.systemHealth || 'Unknown'}</p>
              </div>
              <Globe className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">API Success Rate</span>
                    <Badge className={successRate >= 95 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                      {successRate.toFixed(1)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Average Response Time</span>
                    <Badge className={averageProcessingTime <= 2 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                      {averageProcessingTime.toFixed(1)}s
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Requests</span>
                    <Badge variant="secondary">{stats?.totalRequests || 0}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Failed Requests</span>
                    <Badge variant="destructive">{stats?.failedOptimizations || 0}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  AI Optimization Engine
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Google Gemini API</strong> is active and optimizing your content in real-time.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Features Active:</h4>
                    <ul className="text-sm space-y-1">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        Real-time content analysis
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        SEO title optimization
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        Meta description enhancement
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        Keyword suggestions
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        Content structure analysis
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Optimizations</CardTitle>
              <CardDescription>
                Latest content optimizations performed by the AI engine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics?.recentOptimizations.map((optimization) => (
                  <div key={optimization.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{optimization.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        Optimized {formatTimestamp(optimization.timestamp)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getImpactColor(optimization.impact)}>
                        {optimization.impact.toUpperCase()} Impact
                      </Badge>
                      <Badge variant="outline">
                        {optimization.confidence}/10 Confidence
                      </Badge>
                    </div>
                  </div>
                ))}
                
                {(!metrics?.recentOptimizations || metrics.recentOptimizations.length === 0) && (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent optimizations found. Start creating content to see activity here.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SEO Engine Settings</CardTitle>
              <CardDescription>
                Configure your SEO optimization preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Enhanced SEO System Active:</strong> Your blog editor now includes real-time SEO optimization powered by Google Gemini AI. All optimizations are automatically applied as you write.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Real-time Analysis</h4>
                    <p className="text-sm text-muted-foreground">
                      Get SEO suggestions as you type in the blog editor
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Auto-optimization</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically apply high-confidence SEO improvements
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Performance Monitoring</h4>
                    <p className="text-sm text-muted-foreground">
                      Track optimization success and system health
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button onClick={resetStats} variant="outline" className="w-full">
                  Reset Performance Statistics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SEODashboard;
