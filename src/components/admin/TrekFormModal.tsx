import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Plus, Minus, Trash } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { TrekPackage } from '@/types/database';
import { toast } from '@/hooks/use-toast';

interface TrekFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  trek?: TrekPackage | null;
}

interface DurationVariation {
  days: number;
  price_usd: number;
  itinerary: {
    day: number;
    title: string;
    description: string;
    location?: string;
  }[];
}

const TrekFormModal: React.FC<TrekFormModalProps> = ({ isOpen, onClose, trek }) => {
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    region: '',
    short_description: '',
    long_description: '',
    price_usd: '',
    duration_days: '',
    difficulty_level: '',
    max_altitude: '',
    best_season: '',
    featured: false,
    minimum_days: '',
    duration_variations: { variations: [] as DurationVariation[] }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (trek) {
      setFormData({
        name: trek.name || '',
        slug: trek.slug || '',
        region: trek.region || '',
        short_description: trek.short_description || '',
        long_description: trek.long_description || '',
        price_usd: trek.price_usd?.toString() || '',
        duration_days: trek.duration_days?.toString() || '',
        difficulty_level: trek.difficulty_level || '',
        max_altitude: trek.max_altitude?.toString() || '',
        best_season: trek.best_season || '',
        featured: trek.featured || false,
        minimum_days: trek.minimum_days?.toString() || '',
        duration_variations: trek.duration_variations || { variations: [] }
      });
    } else {
      setFormData({
        name: '',
        slug: '',
        region: '',
        short_description: '',
        long_description: '',
        price_usd: '',
        duration_days: '',
        difficulty_level: '',
        max_altitude: '',
        best_season: '',
        featured: false,
        minimum_days: '',
        duration_variations: { variations: [] }
      });
    }
  }, [trek, isOpen]);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)+/g, '');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    if (name === 'name' && !trek) {
      setFormData(prev => ({
        ...prev,
        slug: generateSlug(value)
      }));
    }
  };

  const addDurationVariation = () => {
    const minDays = parseInt(formData.minimum_days) || 0;
    if (!minDays) {
      toast({
        title: "Error",
        description: "Please set minimum days first",
        variant: "destructive",
      });
      return;
    }

    setFormData(prev => ({
      ...prev,
      duration_variations: {
        variations: [
          ...prev.duration_variations.variations,
          {
            days: minDays + prev.duration_variations.variations.length + 1,
            price_usd: parseInt(formData.price_usd) || 0,
            itinerary: Array(minDays + prev.duration_variations.variations.length + 1)
              .fill(null)
              .map((_, index) => ({
                day: index + 1,
                title: '',
                description: '',
                location: ''
              }))
          }
        ]
      }
    }));
  };

  const removeDurationVariation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      duration_variations: {
        variations: prev.duration_variations.variations.filter((_, i) => i !== index)
      }
    }));
  };

  const updateVariationField = (variationIndex: number, field: keyof DurationVariation, value: number) => {
    setFormData(prev => ({
      ...prev,
      duration_variations: {
        variations: prev.duration_variations.variations.map((variation, i) => 
          i === variationIndex ? { ...variation, [field]: value } : variation
        )
      }
    }));
  };

  const updateItineraryDay = (
    variationIndex: number, 
    dayIndex: number, 
    field: keyof DurationVariation['itinerary'][0], 
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      duration_variations: {
        variations: prev.duration_variations.variations.map((variation, i) => 
          i === variationIndex ? {
            ...variation,
            itinerary: variation.itinerary.map((day, j) =>
              j === dayIndex ? { ...day, [field]: value } : day
            )
          } : variation
        )
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const minDays = parseInt(formData.minimum_days);
      if (!minDays) {
        throw new Error('Minimum days is required');
      }

      const trekData = {
        name: formData.name,
        slug: formData.slug,
        region: formData.region,
        short_description: formData.short_description,
        long_description: formData.long_description,
        price_usd: formData.price_usd ? parseInt(formData.price_usd) : null,
        duration_days: formData.duration_days ? parseInt(formData.duration_days) : null,
        difficulty_level: formData.difficulty_level,
        max_altitude: formData.max_altitude ? parseInt(formData.max_altitude) : null,
        best_season: formData.best_season,
        featured: formData.featured,
        minimum_days: minDays,
        duration_variations: formData.duration_variations,
        itinerary: formData.duration_variations.variations[0]?.itinerary || []
      };

      if (trek) {
        const { error } = await supabase
          .from('trek_packages')
          .update(trekData)
          .eq('id', trek.id);

        if (error) throw error;

        toast({
          title: "Success",
          description: "Trek package updated successfully",
        });
      } else {
        const { error } = await supabase
          .from('trek_packages')
          .insert([trekData]);

        if (error) throw error;

        toast({
          title: "Success",
          description: "Trek package created successfully",
        });
      }

      onClose();
    } catch (error: unknown) {
      console.error('Error saving trek:', error);
      const errorMessage = error instanceof Error ? error.message : 
        typeof error === 'object' && error && 'message' in error ? error.message as string : 
        "Failed to save trek package";
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{trek ? 'Edit Trek Package' : 'Add New Trek Package'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Trek Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="region">Region</Label>
              <Input
                id="region"
                name="region"
                value={formData.region}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="difficulty_level">Difficulty Level</Label>
              <Input
                id="difficulty_level"
                name="difficulty_level"
                value={formData.difficulty_level}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="short_description">Short Description</Label>
            <Textarea
              id="short_description"
              name="short_description"
              value={formData.short_description}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label htmlFor="long_description">Long Description</Label>
            <Textarea
              id="long_description"
              name="long_description"
              value={formData.long_description}
              onChange={handleInputChange}
              rows={4}
              required
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="price_usd">Base Price (USD)</Label>
              <Input
                id="price_usd"
                name="price_usd"
                type="number"
                value={formData.price_usd}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="minimum_days">Minimum Days</Label>
              <Input
                id="minimum_days"
                name="minimum_days"
                type="number"
                value={formData.minimum_days}
                onChange={handleInputChange}
                required
                min="1"
              />
            </div>
            <div>
              <Label htmlFor="max_altitude">Max Altitude (m)</Label>
              <Input
                id="max_altitude"
                name="max_altitude"
                type="number"
                value={formData.max_altitude}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="best_season">Best Season</Label>
              <Input
                id="best_season"
                name="best_season"
                value={formData.best_season}
                onChange={handleInputChange}
              />
            </div>
            <div className="flex items-center space-x-2 pt-6">
              <input
                type="checkbox"
                id="featured"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                className="rounded"
              />
              <Label htmlFor="featured">Featured Package</Label>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Duration Variations</h3>
              <Button 
                type="button" 
                onClick={addDurationVariation}
                variant="outline"
                size="sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Variation
              </Button>
            </div>

            {formData.duration_variations.variations.map((variation, varIndex) => (
              <div key={varIndex} className="border rounded-lg p-4 mb-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium">
                    Variation {varIndex + 1} ({variation.days} days)
                  </h4>
                  <Button
                    type="button"
                    onClick={() => removeDurationVariation(varIndex)}
                    variant="destructive"
                    size="sm"
                  >
                    <Trash className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <Label>Duration (Days)</Label>
                    <Input
                      type="number"
                      value={variation.days}
                      onChange={(e) => updateVariationField(varIndex, 'days', parseInt(e.target.value))}
                      min={parseInt(formData.minimum_days) || 1}
                    />
                  </div>
                  <div>
                    <Label>Price (USD)</Label>
                    <Input
                      type="number"
                      value={variation.price_usd}
                      onChange={(e) => updateVariationField(varIndex, 'price_usd', parseInt(e.target.value))}
                      min={0}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h5 className="font-medium">Daily Itinerary</h5>
                  {variation.itinerary.map((day, dayIndex) => (
                    <div key={dayIndex} className="border-b pb-4">
                      <div className="font-medium mb-2">Day {day.day}</div>
                      <div className="grid gap-4">
                        <div>
                          <Label>Title</Label>
                          <Input
                            value={day.title}
                            onChange={(e) => updateItineraryDay(varIndex, dayIndex, 'title', e.target.value)}
                            placeholder="Day title"
                          />
                        </div>
                        <div>
                          <Label>Location</Label>
                          <Input
                            value={day.location || ''}
                            onChange={(e) => updateItineraryDay(varIndex, dayIndex, 'location', e.target.value)}
                            placeholder="Location/destination"
                          />
                        </div>
                        <div>
                          <Label>Description</Label>
                          <Textarea
                            value={day.description}
                            onChange={(e) => updateItineraryDay(varIndex, dayIndex, 'description', e.target.value)}
                            placeholder="Day's activities and details"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (trek ? 'Update' : 'Create')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TrekFormModal;
