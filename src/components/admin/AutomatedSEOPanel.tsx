/**
 * Automated SEO Panel with Real-time Optimization and Auto-Save
 * Provides seamless, automatic SEO optimization with instant updates
 */

import React, { useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Zap,
  Save,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Sparkles,
  TrendingUp,
  Clock,
  Activity,
  Settings,
  Wand2,
  Target,
  Brain
} from 'lucide-react';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';
import { SEOOptimizationRequest } from '@/lib/seo/enhanced-optimizer';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface AutomatedSEOPanelProps {
  content: string;
  title: string;
  excerpt: string;
  tags: string[];
  blogId?: string;
  onContentUpdate: (field: 'title' | 'excerpt' | 'content' | 'tags', value: string | string[]) => void;
  enableAutoSave?: boolean;
  enableRealTimeOptimization?: boolean;
}

export default function AutomatedSEOPanel({
  content,
  title,
  excerpt,
  tags,
  blogId,
  onContentUpdate,
  enableAutoSave = true,
  enableRealTimeOptimization = true
}: AutomatedSEOPanelProps) {
  const [activeTab, setActiveTab] = useState('optimization');
  const [autoOptimizeEnabled, setAutoOptimizeEnabled] = useState(enableRealTimeOptimization);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(enableAutoSave);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());

  const {
    isOptimizing,
    result,
    error,
    stats,
    health,
    optimizeContent,
    analyzeRealTime,
    checkHealth,
    isHealthy,
    isDegraded,
    successRate,
    averageProcessingTime
  } = useEnhancedSEOOptimizer({
    enableRealTimeAnalysis: autoOptimizeEnabled,
    debounceMs: 2000, // Slightly longer debounce for auto-optimization
    autoRetry: true
  });

  // Auto-optimization effect
  useEffect(() => {
    if (autoOptimizeEnabled && content && title && content.length > 100) {
      console.log('🤖 Auto-optimizing content...');
      analyzeRealTime(content, title, excerpt, tags);
    }
  }, [content, title, excerpt, tags, autoOptimizeEnabled, analyzeRealTime]);

  // Auto-save effect when optimization results are available
  useEffect(() => {
    if (result && autoSaveEnabled && blogId) {
      autoSaveOptimizedContent();
    }
  }, [result, autoSaveEnabled, blogId]);

  // Auto-apply optimization suggestions
  useEffect(() => {
    if (result && autoOptimizeEnabled) {
      autoApplyOptimizations();
    }
  }, [result, autoOptimizeEnabled]);

  const autoSaveOptimizedContent = useCallback(async () => {
    if (!blogId || !result || isSaving) return;

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('blog_posts')
        .update({
          title: result.optimizedTitle || title,
          excerpt: result.optimizedMetaDescription || excerpt,
          updated_at: new Date().toISOString()
        })
        .eq('id', blogId);

      if (error) throw error;

      setLastSaved(new Date());
      toast({
        title: "✅ Auto-saved",
        description: "SEO optimizations have been automatically saved.",
        duration: 2000
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
      toast({
        title: "⚠️ Auto-save failed",
        description: "Failed to save SEO optimizations automatically.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  }, [blogId, result, title, excerpt, isSaving]);

  const autoApplyOptimizations = useCallback(() => {
    if (!result) return;

    // Auto-apply title optimization
    if (result.optimizedTitle && result.optimizedTitle !== title) {
      onContentUpdate('title', result.optimizedTitle);
      setAppliedSuggestions(prev => new Set([...prev, 'title']));
    }

    // Auto-apply meta description optimization
    if (result.optimizedMetaDescription && result.optimizedMetaDescription !== excerpt) {
      onContentUpdate('excerpt', result.optimizedMetaDescription);
      setAppliedSuggestions(prev => new Set([...prev, 'excerpt']));
    }

    // Auto-apply tag suggestions
    if (result.focusKeywords && result.focusKeywords.length > 0) {
      const newTags = [...new Set([...tags, ...result.focusKeywords])];
      if (newTags.length !== tags.length) {
        onContentUpdate('tags', newTags);
        setAppliedSuggestions(prev => new Set([...prev, 'tags']));
      }
    }
  }, [result, title, excerpt, tags, onContentUpdate]);

  const handleManualOptimization = async () => {
    if (!content || !title) {
      toast({
        title: "⚠️ Missing content",
        description: "Please add title and content before optimizing.",
        variant: "destructive"
      });
      return;
    }

    setOptimizationProgress(0);
    const progressInterval = setInterval(() => {
      setOptimizationProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      const request: SEOOptimizationRequest = {
        content,
        title,
        excerpt,
        tags,
        contentType: 'blog',
        priority: 0 // High priority for manual optimization
      };

      await optimizeContent(request);
      setOptimizationProgress(100);

      toast({
        title: "🎯 Optimization complete",
        description: "Content has been optimized for better SEO performance.",
      });
    } catch (error) {
      toast({
        title: "❌ Optimization failed",
        description: error instanceof Error ? error.message : "Failed to optimize content",
        variant: "destructive"
      });
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => setOptimizationProgress(0), 2000);
    }
  };

  const getHealthIcon = () => {
    if (isHealthy) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (isDegraded) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    return <AlertTriangle className="h-4 w-4 text-red-500" />;
  };

  const getHealthStatus = () => {
    if (isHealthy) return "Healthy";
    if (isDegraded) return "Degraded";
    return "Unhealthy";
  };

  const formatLastSaved = () => {
    if (!lastSaved) return "Never";
    const now = new Date();
    const diff = now.getTime() - lastSaved.getTime();
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            AI SEO Automation
          </CardTitle>
          <div className="flex items-center gap-2">
            {getHealthIcon()}
            <span className="text-sm text-muted-foreground">{getHealthStatus()}</span>
          </div>
        </div>

        {/* Auto-save status */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            {isSaving ? (
              <>
                <RefreshCw className="h-3 w-3 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-3 w-3" />
                Last saved: {formatLastSaved()}
              </>
            )}
          </div>
          <div className="flex items-center gap-1">
            {appliedSuggestions.size > 0 && (
              <Badge variant="secondary" className="text-xs">
                {appliedSuggestions.size} applied
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="optimization">Optimization</TabsTrigger>
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="optimization" className="space-y-4">
            {/* Optimization Progress */}
            {optimizationProgress > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Optimizing content...</span>
                  <span>{optimizationProgress}%</span>
                </div>
                <Progress value={optimizationProgress} className="h-2" />
              </div>
            )}

            {/* Manual Optimization Button */}
            <Button
              onClick={handleManualOptimization}
              disabled={isOptimizing || !content || !title}
              className="w-full"
              size="lg"
            >
              {isOptimizing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Optimize Now
                </>
              )}
            </Button>

            {/* Optimization Results */}
            {result && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Optimization Results</span>
                  <Badge variant="outline">
                    Confidence: {result.optimizationConfidence}/10
                  </Badge>
                </div>

                {/* Optimized Title */}
                {result.optimizedTitle && result.optimizedTitle !== title && (
                  <Alert>
                    <Sparkles className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">Optimized Title:</div>
                        <div className="text-sm bg-muted p-2 rounded">
                          {result.optimizedTitle}
                        </div>
                        {appliedSuggestions.has('title') && (
                          <Badge variant="secondary" className="text-xs">
                            ✅ Applied
                          </Badge>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Optimized Meta Description */}
                {result.optimizedMetaDescription && result.optimizedMetaDescription !== excerpt && (
                  <Alert>
                    <Sparkles className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">Optimized Meta Description:</div>
                        <div className="text-sm bg-muted p-2 rounded">
                          {result.optimizedMetaDescription}
                        </div>
                        {appliedSuggestions.has('excerpt') && (
                          <Badge variant="secondary" className="text-xs">
                            ✅ Applied
                          </Badge>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Focus Keywords */}
                {result.focusKeywords && result.focusKeywords.length > 0 && (
                  <Alert>
                    <Target className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">Focus Keywords:</div>
                        <div className="flex flex-wrap gap-1">
                          {result.focusKeywords.map((keyword, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                        {appliedSuggestions.has('tags') && (
                          <Badge variant="secondary" className="text-xs">
                            ✅ Applied
                          </Badge>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Impact Estimation */}
                <div className="flex items-center gap-2 text-sm">
                  <TrendingUp className="h-4 w-4" />
                  <span>Estimated Impact: </span>
                  <Badge
                    variant={result.estimatedImpact === 'high' ? 'default' :
                            result.estimatedImpact === 'medium' ? 'secondary' : 'outline'}
                  >
                    {result.estimatedImpact}
                  </Badge>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="automation" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-optimize">Auto-Optimization</Label>
                  <div className="text-sm text-muted-foreground">
                    Automatically optimize content as you type
                  </div>
                </div>
                <Switch
                  id="auto-optimize"
                  checked={autoOptimizeEnabled}
                  onCheckedChange={setAutoOptimizeEnabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-save">Auto-Save</Label>
                  <div className="text-sm text-muted-foreground">
                    Automatically save optimizations
                  </div>
                </div>
                <Switch
                  id="auto-save"
                  checked={autoSaveEnabled}
                  onCheckedChange={setAutoSaveEnabled}
                />
              </div>

              {autoOptimizeEnabled && (
                <Alert>
                  <Wand2 className="h-4 w-4" />
                  <AlertDescription>
                    AI is actively monitoring your content and will suggest optimizations automatically.
                  </AlertDescription>
                </Alert>
              )}

              {autoSaveEnabled && (
                <Alert>
                  <Save className="h-4 w-4" />
                  <AlertDescription>
                    Optimizations will be saved automatically when available.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                      <p className="text-2xl font-bold">{(successRate || 0).toFixed(1)}%</p>
                    </div>
                    <Target className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Avg Time</p>
                      <p className="text-2xl font-bold">{(averageProcessingTime || 0).toFixed(1)}s</p>
                    </div>
                    <Clock className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {stats && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Total Requests:</span>
                  <span>{stats.totalRequests}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Successful:</span>
                  <span className="text-green-600">{stats.successfulOptimizations}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Failed:</span>
                  <span className="text-red-600">{stats.failedOptimizations}</span>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
