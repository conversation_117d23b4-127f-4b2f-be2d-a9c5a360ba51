
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Eye } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { TrekPackage } from '@/types/database';
import { toast } from '@/hooks/use-toast';
import TrekFormModal from './TrekFormModal';

interface AdminTreksProps {
  onStatsChange: () => void;
}

const AdminTreks: React.FC<AdminTreksProps> = ({ onStatsChange }) => {
  const [treks, setTreks] = useState<TrekPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTrek, setEditingTrek] = useState<TrekPackage | null>(null);

  useEffect(() => {
    fetchTreks();
  }, []);

  const fetchTreks = async () => {
    try {
      const { data, error } = await supabase
        .from('trek_packages')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const transformedTreks = data?.map(trek => ({
        ...trek,
        itinerary: Array.isArray(trek.itinerary) ? trek.itinerary : []
      })) as TrekPackage[] || [];
      
      setTreks(transformedTreks);
    } catch (error) {
      console.error('Error fetching treks:', error);
      toast({
        title: "Error",
        description: "Failed to fetch trek packages",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this trek package?')) return;

    try {
      const { error } = await supabase
        .from('trek_packages')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Trek package deleted successfully",
      });

      fetchTreks();
      onStatsChange();
    } catch (error) {
      console.error('Error deleting trek:', error);
      toast({
        title: "Error",
        description: "Failed to delete trek package",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (trek: TrekPackage) => {
    setEditingTrek(trek);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingTrek(null);
    fetchTreks();
    onStatsChange();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Trek Packages</CardTitle>
          <Button onClick={() => setIsModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Trek Package
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Region</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {treks.map((trek) => (
              <TableRow key={trek.id}>
                <TableCell className="font-medium">{trek.name}</TableCell>
                <TableCell>{trek.region}</TableCell>
                <TableCell>{trek.duration_days} days</TableCell>
                <TableCell>${trek.price_usd}</TableCell>
                <TableCell>{trek.featured ? '✓' : '✗'}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`/treks/${trek.slug}`, '_blank')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(trek)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(trek.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>

      <TrekFormModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        trek={editingTrek}
      />
    </Card>
  );
};

export default AdminTreks;
