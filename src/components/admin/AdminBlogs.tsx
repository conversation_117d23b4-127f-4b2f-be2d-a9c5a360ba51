
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Eye, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { BlogPost } from '@/types/database';
import { toast } from '@/hooks/use-toast';
import BlogFormModal from './BlogFormModal';

interface AdminBlogsProps {
  onStatsChange: () => void;
}

const AdminBlogs: React.FC<AdminBlogsProps> = ({ onStatsChange }) => {
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBlog, setEditingBlog] = useState<BlogPost | null>(null);

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBlogs(data || []);
    } catch (error) {
      console.error('Error fetching blogs:', error);
      toast({
        title: "Error",
        description: "Failed to fetch blog posts",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this blog post?')) return;

    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Blog post deleted successfully",
      });

      fetchBlogs();
      onStatsChange();
    } catch (error) {
      console.error('Error deleting blog:', error);
      toast({
        title: "Error",
        description: "Failed to delete blog post",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (blog: BlogPost) => {
    setEditingBlog(blog);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingBlog(null);
    fetchBlogs();
    onStatsChange();
  };

  const triggerSeoOptimization = async (blogPostId: string) => {
    try {
      console.log(`Triggering SEO optimization for blog post: ${blogPostId}`);
      
      const { data, error } = await supabase.functions.invoke('seo-optimizer', {
        body: { 
          trigger: 'auto_create',
          blogPostId: blogPostId
        }
      });

      if (error) {
        console.error('SEO optimization error:', error);
        return;
      }

      console.log('SEO optimization completed:', data);
      
      toast({
        title: "SEO Optimization",
        description: "Blog post has been optimized for SEO",
      });

    } catch (error) {
      console.error('Error triggering SEO optimization:', error);
    }
  };

  const handleOptimizeBlog = async (blogId: string, blogTitle: string) => {
    try {
      toast({
        title: "SEO Optimization Started",
        description: `Optimizing "${blogTitle}" for better SEO performance`,
      });

      await triggerSeoOptimization(blogId);
      
      // Refresh the blogs list to show updated content
      setTimeout(() => {
        fetchBlogs();
      }, 3000);

    } catch (error) {
      console.error('Error optimizing blog:', error);
      toast({
        title: "Error",
        description: "Failed to optimize blog post",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Blog Posts</CardTitle>
          <Button onClick={() => setIsModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Blog Post
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Published</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>SEO Status</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {blogs.map((blog) => (
              <TableRow key={blog.id}>
                <TableCell className="font-medium">{blog.title}</TableCell>
                <TableCell>{blog.published ? '✓' : '✗'}</TableCell>
                <TableCell>{formatDate(blog.created_at)}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {blog.last_optimized_at ? (
                      <div className="flex items-center group relative">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-1 flex-shrink-0" />
                        <div className="flex flex-col">
                          <span className="text-xs text-green-600">
                            Optimized {blog.optimization_count ?? 1} time{blog.optimization_count !== 1 ? 's' : ''}
                          </span>
                          <span className="text-[10px] text-gray-500">
                            Last: {formatDate(blog.last_optimized_at)}
                          </span>
                        </div>
                        <div className="absolute -top-8 left-0 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                          Last optimized: {new Date(blog.last_optimized_at).toLocaleString()}
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-orange-500 mr-1 flex-shrink-0" />
                        <span className="text-xs text-orange-600">
                          Not yet optimized
                        </span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>{blog.tags.slice(0, 2).join(', ')}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`/blog/${blog.slug}`, '_blank')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(blog)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOptimizeBlog(blog.id, blog.title)}
                      title="Optimize for SEO"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(blog.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>

      <BlogFormModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        blog={editingBlog}
      />
    </Card>
  );
};

export default AdminBlogs;
