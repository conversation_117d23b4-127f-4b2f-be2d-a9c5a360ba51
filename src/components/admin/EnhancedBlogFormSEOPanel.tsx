/**
 * Enhanced Blog Form SEO Panel with Advanced Optimization Features
 * Uses the new enhanced SEO optimizer with circuit breaker, retry logic, and rate limiting
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Info, 
  Check, 
  AlertTriangle, 
  Zap, 
  TrendingUp, 
  Clock, 
  Activity,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';
import { SEOOptimizationRequest } from '@/lib/seo/enhanced-optimizer';

interface EnhancedBlogFormSEOPanelProps {
  content: string;
  title: string;
  excerpt: string;
  tags: string[];
  blogId?: string;
  onSuggestionApply: (field: 'title' | 'meta' | 'tags', value: string | string[]) => void;
  enableRealTimeAnalysis?: boolean;
}

export default function EnhancedBlogFormSEOPanel({
  content,
  title,
  excerpt,
  tags,
  blogId,
  onSuggestionApply,
  enableRealTimeAnalysis = true
}: EnhancedBlogFormSEOPanelProps) {
  const [activeTab, setActiveTab] = useState('analysis');
  
  const {
    isOptimizing,
    result,
    error,
    stats,
    health,
    optimizeContent,
    analyzeRealTime,
    checkHealth,
    resetStats,
    isHealthy,
    isDegraded,
    isUnhealthy,
    successRate,
    averageProcessingTime
  } = useEnhancedSEOOptimizer({
    enableRealTimeAnalysis,
    debounceMs: 1500,
    autoRetry: true
  });

  // Real-time analysis effect
  useEffect(() => {
    if (enableRealTimeAnalysis && content && title) {
      analyzeRealTime(content, title, excerpt, tags);
    }
  }, [content, title, excerpt, tags, enableRealTimeAnalysis, analyzeRealTime]);

  const handleManualOptimization = async () => {
    if (!content || !title) return;

    const request: SEOOptimizationRequest = {
      content,
      title,
      excerpt,
      tags,
      contentType: 'blog',
      priority: 5 // High priority for manual optimization
    };

    try {
      await optimizeContent(request);
    } catch (error) {
      console.error('Manual optimization failed:', error);
    }
  };

  const applySuggestion = (type: 'title' | 'meta' | 'tags', value: string | string[]) => {
    onSuggestionApply(type, value);
  };

  const getHealthIcon = () => {
    if (isHealthy) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (isDegraded) return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    if (isUnhealthy) return <XCircle className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  const getHealthStatus = () => {
    if (isHealthy) return 'Healthy';
    if (isDegraded) return 'Degraded';
    if (isUnhealthy) return 'Unhealthy';
    return 'Unknown';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 8) return 'bg-green-500';
    if (confidence >= 6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getImpactColor = (impact: 'low' | 'medium' | 'high') => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isOptimizing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Analyzing Content...
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Processing SEO optimization</span>
              <span>Please wait...</span>
            </div>
            <Progress value={undefined} className="h-2" />
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
            <div>• Analyzing content structure</div>
            <div>• Optimizing keywords</div>
            <div>• Generating suggestions</div>
            <div>• Calculating confidence score</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Enhanced SEO Analysis
          </CardTitle>
          <div className="flex items-center gap-2">
            {getHealthIcon()}
            <span className="text-sm text-muted-foreground">{getHealthStatus()}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="health">Health</TabsTrigger>
          </TabsList>

          <TabsContent value="analysis" className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {result && (
              <div className="space-y-4">
                {/* Optimization Score */}
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h3 className="font-medium">Optimization Confidence</h3>
                    <p className="text-sm text-muted-foreground">
                      Based on AI analysis and SEO best practices
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">{result.optimizationConfidence}/10</div>
                    <Badge className={getConfidenceColor(result.optimizationConfidence)}>
                      {result.estimatedImpact.toUpperCase()} Impact
                    </Badge>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => applySuggestion('title', result.optimizedTitle)}
                    className="justify-start"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Apply Title
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => applySuggestion('meta', result.optimizedMetaDescription)}
                    className="justify-start"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Apply Meta
                  </Button>
                </div>

                {/* Optimized Content Preview */}
                <div className="space-y-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Optimized Title</label>
                    <div className="p-3 bg-muted rounded border">
                      <p className="text-sm">{result.optimizedTitle}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {result.optimizedTitle.length} characters
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Optimized Meta Description</label>
                    <div className="p-3 bg-muted rounded border">
                      <p className="text-sm">{result.optimizedMetaDescription}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {result.optimizedMetaDescription.length} characters
                      </p>
                    </div>
                  </div>

                  {result.focusKeywords.length > 0 && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Focus Keywords</label>
                      <div className="flex flex-wrap gap-1">
                        {result.focusKeywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={handleManualOptimization}
                disabled={!content || !title}
                className="flex-1"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Optimize Now
              </Button>
              <Button variant="outline" onClick={checkHealth}>
                <Activity className="h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="suggestions" className="space-y-4">
            {result?.contentSuggestions && result.contentSuggestions.length > 0 ? (
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {result.contentSuggestions.map((suggestion, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-sm">{suggestion.section}</h4>
                        <Badge className={getImpactColor(suggestion.impact)}>
                          {suggestion.impact}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {suggestion.reason}
                      </p>
                      <div className="space-y-2">
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">Current:</label>
                          <p className="text-sm bg-red-50 p-2 rounded border">
                            {suggestion.currentText}
                          </p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">Suggested:</label>
                          <p className="text-sm bg-green-50 p-2 rounded border">
                            {suggestion.suggestedText}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Info className="h-8 w-8 mx-auto mb-2" />
                <p>No content suggestions available</p>
                <p className="text-sm">Run optimization to get suggestions</p>
              </div>
            )}

            {result?.technicalRecommendations && result.technicalRecommendations.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Technical Recommendations</h4>
                <ul className="space-y-1">
                  {result.technicalRecommendations.map((rec, index) => (
                    <li key={index} className="text-sm flex items-start gap-2">
                      <Check className="h-3 w-3 mt-0.5 text-green-500 flex-shrink-0" />
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            {stats && (
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{stats.totalRequests}</div>
                  <p className="text-sm text-muted-foreground">Total Requests</p>
                </div>
                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {successRate.toFixed(1)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Success Rate</p>
                </div>
                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{averageProcessingTime.toFixed(0)}ms</div>
                  <p className="text-sm text-muted-foreground">Avg Processing Time</p>
                </div>
                <div className="p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{stats.failedOptimizations}</div>
                  <p className="text-sm text-muted-foreground">Failed Requests</p>
                </div>
              </div>
            )}

            {result && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Last Optimization</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Processed in {result.processingTime}ms with {result.optimizationConfidence}/10 confidence
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Version: {result.version}
                </p>
              </div>
            )}

            <Button variant="outline" onClick={resetStats} className="w-full">
              Reset Statistics
            </Button>
          </TabsContent>

          <TabsContent value="health" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">System Health</h4>
                  {getHealthIcon()}
                </div>
                <p className="text-sm text-muted-foreground">
                  Status: {getHealthStatus()}
                </p>
                {health?.details && (
                  <pre className="text-xs bg-muted p-2 rounded mt-2 overflow-auto">
                    {JSON.stringify(health.details, null, 2)}
                  </pre>
                )}
              </div>

              {stats && (
                <>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Circuit Breaker</h4>
                    <div className="space-y-1 text-sm">
                      <p>State: <Badge variant="outline">{stats.circuitBreakerStats.state}</Badge></p>
                      <p>Failures: {stats.circuitBreakerStats.failureCount}</p>
                      <p>Successes: {stats.circuitBreakerStats.successCount}</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Rate Limiting</h4>
                    <div className="space-y-1 text-sm">
                      <p>Available Tokens: {stats.rateLimitStats.currentTokens}</p>
                      <p>Queue Length: {stats.rateLimitStats.queueLength}</p>
                      <p>Avg Wait Time: {stats.rateLimitStats.averageWaitTime.toFixed(0)}ms</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Retry Handler</h4>
                    <div className="space-y-1 text-sm">
                      <p>Total Attempts: {stats.retryStats.totalAttempts}</p>
                      <p>Successful Retries: {stats.retryStats.successfulRetries}</p>
                      <p>Failed Retries: {stats.retryStats.failedRetries}</p>
                    </div>
                  </div>
                </>
              )}
            </div>

            <Button onClick={checkHealth} className="w-full">
              <Activity className="h-4 w-4 mr-2" />
              Check Health
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
