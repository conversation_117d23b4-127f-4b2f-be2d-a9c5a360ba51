/**
 * Enhanced SEO Optimizer with Circuit Breaker, Retry Logic, and Rate Limiting
 * Provides robust, production-ready SEO optimization capabilities
 * Updated to use modern Google GenAI SDK
 */

import { CircuitBreaker, defaultSEOCircuitBreakerConfig, CircuitBreakerConfig, CircuitBreakerStats } from './circuit-breaker';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, defaultSEORetryConfig, RetryConfig, RetryStats } from './retry-handler';
import { RateLimiter, defaultSEORateLimitConfig, RateLimitConfig, RateLimitStats } from './rate-limiter';
import { GeminiService, createGeminiService } from './gemini-service';
// import { supabase } from '@/integrations/supabase/client'; // TODO: Add logging integration

export interface SEOOptimizationRequest {
  content: string;
  title: string;
  excerpt?: string;
  tags?: string[];
  targetKeywords?: string[];
  contentType: 'blog' | 'trek' | 'page';
  priority?: number; // 0-10, higher = more priority
}

export interface SEOOptimizationResponse {
  optimizedTitle: string;
  optimizedMetaDescription: string;
  focusKeywords: string[];
  contentSuggestions: ContentSuggestion[];
  technicalRecommendations: string[];
  optimizationConfidence: number;
  estimatedImpact: 'low' | 'medium' | 'high';
  processingTime: number;
  version: string;
}

export interface ContentSuggestion {
  section: string;
  currentText: string;
  suggestedText: string;
  reason: string;
  impact: 'low' | 'medium' | 'high';
}

export interface OptimizationStats {
  totalRequests: number;
  successfulOptimizations: number;
  failedOptimizations: number;
  averageProcessingTime: number;
  circuitBreakerStats: CircuitBreakerStats;
  retryStats: RetryStats;
  rateLimitStats: RateLimitStats;
}

export interface EnhancedOptimizerConfig {
  circuitBreaker?: CircuitBreakerConfig;
  retry?: RetryConfig;
  rateLimit?: RateLimitConfig;
  geminiApiKey?: string;
  enableFallback?: boolean;
  maxContentLength?: number;
  optimizationTimeout?: number;
}

export class EnhancedSEOOptimizer {
  private circuitBreaker: CircuitBreaker;
  private retryHandler: RetryHandler;
  private rateLimiter: RateLimiter;
  private stats: OptimizationStats;
  private geminiService: GeminiService;

  constructor(config: EnhancedOptimizerConfig = {}) {
    this.circuitBreaker = new CircuitBreaker(
      config.circuitBreaker || defaultSEOCircuitBreakerConfig
    );
    this.retryHandler = new RetryHandler(
      config.retry || defaultSEORetryConfig
    );
    this.rateLimiter = new RateLimiter(
      config.rateLimit || defaultSEORateLimitConfig
    );

    // Initialize Gemini service with modern SDK
    const apiKey = config.geminiApiKey || import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key is required for SEO optimization');
    }

    this.geminiService = createGeminiService(apiKey);

    this.stats = {
      totalRequests: 0,
      successfulOptimizations: 0,
      failedOptimizations: 0,
      averageProcessingTime: 0,
      circuitBreakerStats: this.circuitBreaker.getStats(),
      retryStats: this.retryHandler.getStats(),
      rateLimitStats: this.rateLimiter.getStats()
    };
  }

  async optimizeContent(request: SEOOptimizationRequest): Promise<SEOOptimizationResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Validate input
      this.validateRequest(request);

      // Execute with rate limiting, circuit breaker, and retry logic
      const result = await this.rateLimiter.execute(async () => {
        return await this.circuitBreaker.execute(async () => {
          return await this.retryHandler.execute(async () => {
            return await this.performOptimization(request);
          }, `SEO optimization for ${request.contentType}`);
        });
      }, request.priority || 0);

      const processingTime = Date.now() - startTime;
      this.updateSuccessStats(processingTime);

      return {
        ...result,
        processingTime,
        version: '2.0.0'
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateFailureStats(processingTime);

      // Try fallback if enabled
      if (this.shouldUseFallback(error)) {
        console.warn('Using fallback optimization due to error:', error);
        return this.getFallbackOptimization(request, processingTime);
      }

      throw new Error(`SEO optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async performOptimization(request: SEOOptimizationRequest): Promise<Omit<SEOOptimizationResponse, 'processingTime' | 'version'>> {
    try {
      // Use the new Gemini service for SEO content generation
      const seoResult = await this.geminiService.generateSEOContent(
        request.content,
        request.title,
        request.excerpt,
        request.tags,
        request.contentType
      );

      // Convert the Gemini service response to our SEO optimization format
      return {
        optimizedTitle: seoResult.optimizedTitle,
        optimizedMetaDescription: seoResult.optimizedExcerpt,
        focusKeywords: this.extractKeywords(seoResult.optimizedContent, request.targetKeywords),
        contentSuggestions: this.generateContentSuggestions(request, seoResult),
        technicalRecommendations: this.generateTechnicalRecommendations(request, seoResult),
        optimizationConfidence: this.calculateOptimizationConfidence(request, seoResult),
        estimatedImpact: this.estimateImpact(request, seoResult)
      };
    } catch (error) {
      // Enhanced error handling with more specific error types
      if (error instanceof Error) {
        // Re-throw with additional context for debugging
        throw new Error(`SEO optimization failed: ${error.message}`);
      }

      throw new Error('Unknown error occurred during SEO optimization');
    }
  }

  private buildOptimizationPrompt(request: SEOOptimizationRequest): string {
    const { content, title, excerpt, tags, targetKeywords, contentType } = request;

    return `You are an expert SEO specialist for Nepal travel and adventure content. Your goal is to optimize content for travelers searching for Nepal trekking, adventure tours, and cultural experiences.

CURRENT CONTENT ANALYSIS:
- Title: ${title}
- Meta Description: ${excerpt || "Not provided"}
- Current Tags: ${tags?.join(', ') || 'Not provided'}
- Target Keywords: ${targetKeywords?.join(', ') || 'Not provided'}
- Content Type: ${contentType}

CONTENT TO OPTIMIZE:
${content.substring(0, 4000)}${content.length > 4000 ? '...' : ''}

SEO OPTIMIZATION REQUIREMENTS:
1. **Title Optimization**: Create a compelling title (50-60 characters) that includes primary keywords and appeals to adventure travelers
2. **Meta Description**: Write an engaging meta description (150-160 characters) that includes a call-to-action
3. **Keywords**: Suggest 3-5 high-value keywords focusing on Nepal travel, trekking, and adventure tourism
4. **Content Improvements**: Provide specific, actionable recommendations to improve SEO performance
5. **Technical SEO**: Consider search intent, semantic keywords, and user experience
6. **Confidence Rating**: Rate your optimization confidence (1-10) based on content quality and keyword potential

FOCUS AREAS FOR NEPAL TRAVEL CONTENT:
- Include location-specific keywords (e.g., "Nepal", "Himalayas", "Kathmandu")
- Target adventure and trekking terms (e.g., "trekking", "expedition", "adventure")
- Consider seasonal and difficulty-based keywords
- Include practical information keywords (e.g., "guide", "cost", "best time")
- Appeal to different traveler types (solo, family, experienced trekkers)

Return your response as a JSON object with this structure:
{
  "optimizedTitle": "string",
  "optimizedMetaDescription": "string",
  "focusKeywords": ["string"],
  "contentSuggestions": [{
    "section": "string",
    "currentText": "string",
    "suggestedText": "string",
    "reason": "string",
    "impact": "low|medium|high"
  }],
  "technicalRecommendations": ["string"],
  "optimizationConfidence": number,
  "estimatedImpact": "low|medium|high"
}`;
  }

  private parseOptimizationResponse(
    response: string,
    _request: SEOOptimizationRequest
  ): Omit<SEOOptimizationResponse, 'processingTime' | 'version'> {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate required fields
      if (!parsed.optimizedTitle || !parsed.optimizedMetaDescription) {
        throw new Error('Missing required optimization fields');
      }

      return {
        optimizedTitle: parsed.optimizedTitle,
        optimizedMetaDescription: parsed.optimizedMetaDescription,
        focusKeywords: parsed.focusKeywords || [],
        contentSuggestions: parsed.contentSuggestions || [],
        technicalRecommendations: parsed.technicalRecommendations || [],
        optimizationConfidence: parsed.optimizationConfidence || 5,
        estimatedImpact: parsed.estimatedImpact || 'medium'
      };
    } catch (error) {
      console.error('Failed to parse optimization response:', error);
      throw new Error(`Failed to parse optimization response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private validateRequest(request: SEOOptimizationRequest): void {
    if (!request.content || request.content.trim().length === 0) {
      throw new Error('Content is required for optimization');
    }

    if (!request.title || request.title.trim().length === 0) {
      throw new Error('Title is required for optimization');
    }

    if (request.content.length > 10000) {
      throw new Error('Content is too long for optimization (max 10,000 characters)');
    }
  }

  private shouldUseFallback(error: unknown): boolean {
    if (!(error instanceof Error)) return false;

    const fallbackTriggers = [
      'rate limit',
      'quota exceeded',
      'service unavailable',
      'timeout'
    ];

    return fallbackTriggers.some(trigger =>
      error.message.toLowerCase().includes(trigger)
    );
  }

  private getFallbackOptimization(
    request: SEOOptimizationRequest,
    processingTime: number
  ): SEOOptimizationResponse {
    // Basic fallback optimization using simple rules
    const optimizedTitle = this.generateFallbackTitle(request.title, request.tags);
    const optimizedMetaDescription = this.generateFallbackMetaDescription(request.excerpt, request.content);

    return {
      optimizedTitle,
      optimizedMetaDescription,
      focusKeywords: request.tags?.slice(0, 5) || [],
      contentSuggestions: [],
      technicalRecommendations: [
        'AI optimization temporarily unavailable - using basic optimization',
        'Consider manual review of title and meta description',
        'Retry optimization later for enhanced suggestions'
      ],
      optimizationConfidence: 3,
      estimatedImpact: 'low',
      processingTime,
      version: '2.0.0-fallback'
    };
  }

  private generateFallbackTitle(title: string, _tags?: string[]): string {
    if (title.length <= 60) {
      return title;
    }

    // Truncate and add ellipsis
    return title.substring(0, 57).trim() + '...';
  }

  private generateFallbackMetaDescription(excerpt?: string, content?: string): string {
    const source = excerpt || content?.substring(0, 200) || '';

    if (source.length <= 160) {
      return source;
    }

    return source.substring(0, 157).trim() + '...';
  }

  private updateSuccessStats(processingTime: number): void {
    this.stats.successfulOptimizations++;
    this.updateAverageProcessingTime(processingTime);
    this.updateComponentStats();
  }

  private updateFailureStats(processingTime: number): void {
    this.stats.failedOptimizations++;
    this.updateAverageProcessingTime(processingTime);
    this.updateComponentStats();
  }

  private updateAverageProcessingTime(processingTime: number): void {
    const totalTime = this.stats.averageProcessingTime * (this.stats.totalRequests - 1) + processingTime;
    this.stats.averageProcessingTime = totalTime / this.stats.totalRequests;
  }

  private updateComponentStats(): void {
    this.stats.circuitBreakerStats = this.circuitBreaker.getStats();
    this.stats.retryStats = this.retryHandler.getStats();
    this.stats.rateLimitStats = this.rateLimiter.getStats();
  }

  // Public methods for monitoring and management
  getStats(): OptimizationStats {
    this.updateComponentStats();
    return { ...this.stats };
  }

  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulOptimizations: 0,
      failedOptimizations: 0,
      averageProcessingTime: 0,
      circuitBreakerStats: this.circuitBreaker.getStats(),
      retryStats: this.retryHandler.getStats(),
      rateLimitStats: this.rateLimiter.getStats()
    };

    this.circuitBreaker.reset();
    this.retryHandler.reset();
  }

  destroy(): void {
    this.rateLimiter.destroy();
  }

  // Health check
  async healthCheck(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; details: Record<string, unknown> }> {
    const circuitStats = this.circuitBreaker.getStats();
    const rateLimitStats = this.rateLimiter.getStats();

    // Also check Gemini service health
    try {
      const geminiHealth = await this.geminiService.healthCheck();

      if (geminiHealth.status === 'unhealthy') {
        return {
          status: 'unhealthy',
          details: { reason: 'Gemini service is unhealthy', geminiHealth, circuitStats, rateLimitStats }
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { reason: 'Failed to check Gemini service health', error: error instanceof Error ? error.message : 'Unknown error', circuitStats, rateLimitStats }
      };
    }

    if (circuitStats.state === 'OPEN') {
      return {
        status: 'unhealthy',
        details: { reason: 'Circuit breaker is open', circuitStats, rateLimitStats }
      };
    }

    if (rateLimitStats.queueLength > rateLimitStats.queueLength * 0.8) {
      return {
        status: 'degraded',
        details: { reason: 'Rate limit queue is nearly full', circuitStats, rateLimitStats }
      };
    }

    return {
      status: 'healthy',
      details: { circuitStats, rateLimitStats }
    };
  }

  // Helper methods for SEO optimization
  private extractKeywords(content: string, targetKeywords?: string[]): string[] {
    const keywords = new Set<string>();

    // Add target keywords if provided
    if (targetKeywords) {
      targetKeywords.forEach(keyword => keywords.add(keyword.toLowerCase()));
    }

    // Extract common Nepal travel keywords from content
    const nepalKeywords = [
      'nepal', 'himalaya', 'everest', 'annapurna', 'kathmandu', 'pokhara',
      'trekking', 'hiking', 'adventure', 'expedition', 'mountain', 'peak',
      'sherpa', 'monastery', 'culture', 'temple', 'festival', 'rhododendron'
    ];

    const contentLower = content.toLowerCase();
    nepalKeywords.forEach(keyword => {
      if (contentLower.includes(keyword)) {
        keywords.add(keyword);
      }
    });

    return Array.from(keywords).slice(0, 5);
  }

  private generateContentSuggestions(
    request: SEOOptimizationRequest,
    seoResult: { optimizedTitle: string; optimizedExcerpt: string; optimizedContent: string; optimizedTags: string[] }
  ): ContentSuggestion[] {
    const suggestions: ContentSuggestion[] = [];

    // Title suggestion
    if (request.title !== seoResult.optimizedTitle) {
      suggestions.push({
        section: 'title',
        currentText: request.title,
        suggestedText: seoResult.optimizedTitle,
        reason: 'Optimized for better SEO performance and click-through rates',
        impact: 'high'
      });
    }

    // Meta description suggestion
    if (request.excerpt && request.excerpt !== seoResult.optimizedExcerpt) {
      suggestions.push({
        section: 'meta_description',
        currentText: request.excerpt,
        suggestedText: seoResult.optimizedExcerpt,
        reason: 'Improved meta description for better search result snippets',
        impact: 'medium'
      });
    }

    // Content structure suggestions
    if (request.content.length < 300) {
      suggestions.push({
        section: 'content_length',
        currentText: `Content length: ${request.content.length} characters`,
        suggestedText: 'Expand content to at least 300 words for better SEO',
        reason: 'Longer content typically performs better in search results',
        impact: 'medium'
      });
    }

    return suggestions;
  }

  private generateTechnicalRecommendations(
    request: SEOOptimizationRequest,
    seoResult: { optimizedTitle: string; optimizedExcerpt: string; optimizedContent: string; optimizedTags: string[] }
  ): string[] {
    const recommendations: string[] = [];

    // Title length check
    if (seoResult.optimizedTitle.length > 60) {
      recommendations.push('Consider shortening the title to under 60 characters for better display in search results');
    }

    // Meta description length check
    if (seoResult.optimizedExcerpt.length > 160) {
      recommendations.push('Meta description should be under 160 characters for optimal display');
    }

    // Content structure recommendations
    if (!request.content.includes('##') && !request.content.includes('#')) {
      recommendations.push('Add heading tags (H2, H3) to improve content structure and SEO');
    }

    // Image recommendations
    if (!request.content.includes('![') && !request.content.includes('<img')) {
      recommendations.push('Consider adding relevant images with alt text for better user engagement');
    }

    // Internal linking
    if (!request.content.includes('[') && !request.content.includes('<a')) {
      recommendations.push('Add internal links to related content to improve site navigation and SEO');
    }

    // Location-specific recommendations for Nepal content
    if (request.contentType === 'trek' || request.contentType === 'blog') {
      const hasLocationInfo = /\b(altitude|elevation|distance|duration|difficulty)\b/i.test(request.content);
      if (!hasLocationInfo) {
        recommendations.push('Include specific trek details like altitude, distance, and difficulty level');
      }
    }

    return recommendations;
  }

  private calculateOptimizationConfidence(
    request: SEOOptimizationRequest,
    seoResult: { optimizedTitle: string; optimizedExcerpt: string; optimizedContent: string; optimizedTags: string[] }
  ): number {
    let confidence = 5; // Base confidence

    // Increase confidence based on content quality
    if (request.content.length > 500) confidence += 1;
    if (request.content.length > 1000) confidence += 1;

    // Increase confidence if we have target keywords
    if (request.targetKeywords && request.targetKeywords.length > 0) confidence += 1;

    // Increase confidence if we have tags
    if (request.tags && request.tags.length > 0) confidence += 1;

    // Increase confidence if content has good structure
    if (request.content.includes('#') || request.content.includes('##')) confidence += 1;

    // Increase confidence based on optimization quality
    if (seoResult.optimizedTitle.length <= 60 && seoResult.optimizedTitle.length > 30) confidence += 1;
    if (seoResult.optimizedExcerpt.length <= 160 && seoResult.optimizedExcerpt.length > 120) confidence += 1;
    if (seoResult.optimizedTags.length >= 3) confidence += 1;

    // Cap at 10
    return Math.min(confidence, 10);
  }

  private estimateImpact(
    request: SEOOptimizationRequest,
    seoResult: { optimizedTitle: string; optimizedExcerpt: string; optimizedContent: string; optimizedTags: string[] }
  ): 'low' | 'medium' | 'high' {
    let impactScore = 0;

    // Title optimization impact
    if (request.title !== seoResult.optimizedTitle) {
      const titleImprovement = seoResult.optimizedTitle.length <= 60 &&
                              seoResult.optimizedTitle.toLowerCase().includes('nepal');
      if (titleImprovement) impactScore += 2;
      else impactScore += 1;
    }

    // Meta description impact
    if (request.excerpt !== seoResult.optimizedExcerpt) {
      impactScore += 1;
    }

    // Content type impact
    if (request.contentType === 'blog' && request.content.length > 800) {
      impactScore += 1;
    }

    // Tags impact
    if (seoResult.optimizedTags.length >= 3) {
      impactScore += 1;
    }

    if (impactScore >= 4) return 'high';
    if (impactScore >= 2) return 'medium';
    return 'low';
  }
}
