/**
 * Rate Limiter for SEO API Operations
 * Implements token bucket algorithm with request queuing
 */

export interface RateLimitConfig {
  maxTokens: number;
  refillRate: number; // tokens per second
  refillInterval: number; // milliseconds
  queueSize: number;
  requestTimeout: number;
}

export interface QueuedRequest<T> {
  id: string;
  operation: () => Promise<T>;
  resolve: (value: T) => void;
  reject: (error: Error) => void;
  timestamp: Date;
  timeout: NodeJS.Timeout;
}

export interface RateLimitStats {
  currentTokens: number;
  queueLength: number;
  totalRequests: number;
  successfulRequests: number;
  rejectedRequests: number;
  averageWaitTime: number;
  lastRefill: Date;
}

export class RateLimiter {
  private tokens: number;
  private lastRefill: Date;
  private queue: QueuedRequest<any>[] = [];
  private stats: RateLimitStats;
  private refillTimer?: NodeJS.Timeout;

  constructor(private config: RateLimitConfig) {
    this.tokens = config.maxTokens;
    this.lastRefill = new Date();
    this.stats = {
      currentTokens: this.tokens,
      queueLength: 0,
      totalRequests: 0,
      successfulRequests: 0,
      rejectedRequests: 0,
      averageWaitTime: 0,
      lastRefill: this.lastRefill
    };

    this.startRefillTimer();
  }

  async execute<T>(operation: () => Promise<T>, priority: number = 0): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const requestId = this.generateRequestId();
      const startTime = Date.now();

      // Check if we can execute immediately
      if (this.tokens > 0 && this.queue.length === 0) {
        this.consumeToken();
        this.executeOperation(operation, resolve, reject, startTime);
        return;
      }

      // Check queue capacity
      if (this.queue.length >= this.config.queueSize) {
        this.stats.rejectedRequests++;
        reject(new Error('Rate limit queue is full'));
        return;
      }

      // Add to queue
      const timeout = setTimeout(() => {
        this.removeFromQueue(requestId);
        this.stats.rejectedRequests++;
        reject(new Error('Request timeout in rate limit queue'));
      }, this.config.requestTimeout);

      const queuedRequest: QueuedRequest<T> = {
        id: requestId,
        operation,
        resolve: (value: T) => {
          clearTimeout(timeout);
          this.updateWaitTime(startTime);
          resolve(value);
        },
        reject: (error: Error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timestamp: new Date(),
        timeout
      };

      // Insert based on priority (higher priority first)
      const insertIndex = this.queue.findIndex(req => priority > 0);
      if (insertIndex === -1) {
        this.queue.push(queuedRequest);
      } else {
        this.queue.splice(insertIndex, 0, queuedRequest);
      }

      this.stats.queueLength = this.queue.length;
      this.processQueue();
    });
  }

  private async executeOperation<T>(
    operation: () => Promise<T>,
    resolve: (value: T) => void,
    reject: (error: Error) => void,
    startTime: number
  ): Promise<void> {
    try {
      this.stats.totalRequests++;
      const result = await operation();
      this.stats.successfulRequests++;
      this.updateWaitTime(startTime);
      resolve(result);
    } catch (error) {
      this.stats.rejectedRequests++;
      reject(error instanceof Error ? error : new Error(String(error)));
    }
  }

  private processQueue(): void {
    while (this.queue.length > 0 && this.tokens > 0) {
      const request = this.queue.shift()!;
      this.consumeToken();
      
      const startTime = Date.now();
      this.executeOperation(
        request.operation,
        request.resolve,
        request.reject,
        startTime
      );
    }
    
    this.stats.queueLength = this.queue.length;
  }

  private consumeToken(): void {
    if (this.tokens > 0) {
      this.tokens--;
      this.stats.currentTokens = this.tokens;
    }
  }

  private refillTokens(): void {
    const now = new Date();
    const timeSinceLastRefill = now.getTime() - this.lastRefill.getTime();
    const tokensToAdd = Math.floor((timeSinceLastRefill / 1000) * this.config.refillRate);

    if (tokensToAdd > 0) {
      this.tokens = Math.min(this.config.maxTokens, this.tokens + tokensToAdd);
      this.lastRefill = now;
      this.stats.currentTokens = this.tokens;
      this.stats.lastRefill = this.lastRefill;
      
      // Process queue if we have new tokens
      if (this.queue.length > 0) {
        this.processQueue();
      }
    }
  }

  private startRefillTimer(): void {
    this.refillTimer = setInterval(() => {
      this.refillTokens();
    }, this.config.refillInterval);
  }

  private removeFromQueue(requestId: string): void {
    const index = this.queue.findIndex(req => req.id === requestId);
    if (index !== -1) {
      this.queue.splice(index, 1);
      this.stats.queueLength = this.queue.length;
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateWaitTime(startTime: number): void {
    const waitTime = Date.now() - startTime;
    const totalWaitTime = this.stats.averageWaitTime * this.stats.successfulRequests + waitTime;
    this.stats.averageWaitTime = totalWaitTime / (this.stats.successfulRequests + 1);
  }

  getStats(): RateLimitStats {
    return { ...this.stats };
  }

  destroy(): void {
    if (this.refillTimer) {
      clearInterval(this.refillTimer);
    }
    
    // Reject all queued requests
    this.queue.forEach(request => {
      clearTimeout(request.timeout);
      request.reject(new Error('Rate limiter destroyed'));
    });
    
    this.queue = [];
  }

  // Manual token management
  addTokens(count: number): void {
    this.tokens = Math.min(this.config.maxTokens, this.tokens + count);
    this.stats.currentTokens = this.tokens;
    this.processQueue();
  }

  getAvailableTokens(): number {
    return this.tokens;
  }

  getQueueLength(): number {
    return this.queue.length;
  }
}

// Default configuration for SEO operations
export const defaultSEORateLimitConfig: RateLimitConfig = {
  maxTokens: 10, // 10 concurrent requests
  refillRate: 2, // 2 tokens per second
  refillInterval: 500, // Check every 500ms
  queueSize: 50, // Max 50 queued requests
  requestTimeout: 30000 // 30 second timeout
};
