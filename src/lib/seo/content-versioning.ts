/**
 * Content Versioning System for SEO Optimizations
 * Tracks changes, provides rollback functionality, and maintains optimization history
 */

export interface ContentVersion {
  id: string;
  contentId: string;
  contentType: 'blog' | 'trek' | 'page';
  version: number;
  title: string;
  content: string;
  metaDescription: string;
  tags: string[];
  seoScore: number;
  optimizationData: OptimizationMetadata;
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
  parentVersionId?: string;
}

export interface OptimizationMetadata {
  optimizationType: 'manual' | 'automated' | 'ai-enhanced';
  confidence: number;
  estimatedImpact: 'low' | 'medium' | 'high';
  focusKeywords: string[];
  technicalRecommendations: string[];
  processingTime: number;
  aiModel: string;
  optimizerVersion: string;
}

export interface VersionComparison {
  oldVersion: ContentVersion;
  newVersion: ContentVersion;
  changes: ContentChange[];
  impactAnalysis: ImpactAnalysis;
}

export interface ContentChange {
  field: 'title' | 'content' | 'metaDescription' | 'tags';
  type: 'added' | 'removed' | 'modified';
  oldValue: string | string[];
  newValue: string | string[];
  impact: 'low' | 'medium' | 'high';
  reason: string;
}

export interface ImpactAnalysis {
  seoScoreChange: number;
  confidenceChange: number;
  estimatedTrafficImpact: number;
  keywordImprovements: string[];
  potentialRisks: string[];
  recommendations: string[];
}

export interface VersioningStats {
  totalVersions: number;
  activeVersions: number;
  averageOptimizationGain: number;
  mostImprovedContent: ContentVersion[];
  recentOptimizations: ContentVersion[];
}

export class ContentVersioningManager {
  private versions: Map<string, ContentVersion[]> = new Map();
  private versionCounter = 0;

  constructor() {
    this.loadVersionsFromStorage();
  }

  /**
   * Create a new version of content
   */
  async createVersion(
    contentId: string,
    contentType: 'blog' | 'trek' | 'page',
    data: {
      title: string;
      content: string;
      metaDescription: string;
      tags: string[];
      seoScore: number;
      optimizationData: OptimizationMetadata;
      createdBy: string;
      parentVersionId?: string;
    }
  ): Promise<ContentVersion> {
    const existingVersions = this.versions.get(contentId) || [];
    const version = existingVersions.length + 1;

    // Deactivate previous active version
    existingVersions.forEach(v => v.isActive = false);

    const newVersion: ContentVersion = {
      id: this.generateVersionId(),
      contentId,
      contentType,
      version,
      title: data.title,
      content: data.content,
      metaDescription: data.metaDescription,
      tags: [...data.tags],
      seoScore: data.seoScore,
      optimizationData: { ...data.optimizationData },
      createdAt: new Date(),
      createdBy: data.createdBy,
      isActive: true,
      parentVersionId: data.parentVersionId
    };

    existingVersions.push(newVersion);
    this.versions.set(contentId, existingVersions);
    
    await this.saveVersionsToStorage();
    return newVersion;
  }

  /**
   * Get all versions for a content item
   */
  getVersions(contentId: string): ContentVersion[] {
    return this.versions.get(contentId) || [];
  }

  /**
   * Get the active version for a content item
   */
  getActiveVersion(contentId: string): ContentVersion | null {
    const versions = this.getVersions(contentId);
    return versions.find(v => v.isActive) || null;
  }

  /**
   * Get a specific version by ID
   */
  getVersion(versionId: string): ContentVersion | null {
    for (const versions of this.versions.values()) {
      const version = versions.find(v => v.id === versionId);
      if (version) return version;
    }
    return null;
  }

  /**
   * Rollback to a previous version
   */
  async rollbackToVersion(versionId: string, rollbackBy: string): Promise<ContentVersion> {
    const targetVersion = this.getVersion(versionId);
    if (!targetVersion) {
      throw new Error('Version not found');
    }

    const contentId = targetVersion.contentId;
    const versions = this.getVersions(contentId);
    
    // Deactivate current active version
    versions.forEach(v => v.isActive = false);

    // Create a new version based on the target version
    const rollbackVersion = await this.createVersion(
      contentId,
      targetVersion.contentType,
      {
        title: targetVersion.title,
        content: targetVersion.content,
        metaDescription: targetVersion.metaDescription,
        tags: targetVersion.tags,
        seoScore: targetVersion.seoScore,
        optimizationData: {
          ...targetVersion.optimizationData,
          optimizationType: 'manual',
          confidence: targetVersion.optimizationData.confidence * 0.9, // Slight confidence reduction for rollback
        },
        createdBy: rollbackBy,
        parentVersionId: targetVersion.id
      }
    );

    return rollbackVersion;
  }

  /**
   * Compare two versions
   */
  compareVersions(oldVersionId: string, newVersionId: string): VersionComparison | null {
    const oldVersion = this.getVersion(oldVersionId);
    const newVersion = this.getVersion(newVersionId);

    if (!oldVersion || !newVersion) {
      return null;
    }

    const changes = this.calculateChanges(oldVersion, newVersion);
    const impactAnalysis = this.analyzeImpact(oldVersion, newVersion, changes);

    return {
      oldVersion,
      newVersion,
      changes,
      impactAnalysis
    };
  }

  /**
   * Get optimization history for a content item
   */
  getOptimizationHistory(contentId: string): ContentVersion[] {
    return this.getVersions(contentId)
      .filter(v => v.optimizationData.optimizationType !== 'manual')
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * Get versioning statistics
   */
  getStats(): VersioningStats {
    const allVersions = Array.from(this.versions.values()).flat();
    const activeVersions = allVersions.filter(v => v.isActive);
    
    // Calculate average optimization gain
    const optimizationGains = allVersions
      .filter(v => v.parentVersionId)
      .map(v => {
        const parent = this.getVersion(v.parentVersionId!);
        return parent ? v.seoScore - parent.seoScore : 0;
      })
      .filter(gain => gain > 0);

    const averageOptimizationGain = optimizationGains.length > 0
      ? optimizationGains.reduce((sum, gain) => sum + gain, 0) / optimizationGains.length
      : 0;

    // Get most improved content
    const mostImprovedContent = activeVersions
      .filter(v => v.parentVersionId)
      .map(v => {
        const parent = this.getVersion(v.parentVersionId!);
        return {
          version: v,
          improvement: parent ? v.seoScore - parent.seoScore : 0
        };
      })
      .sort((a, b) => b.improvement - a.improvement)
      .slice(0, 5)
      .map(item => item.version);

    // Get recent optimizations
    const recentOptimizations = allVersions
      .filter(v => v.optimizationData.optimizationType !== 'manual')
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    return {
      totalVersions: allVersions.length,
      activeVersions: activeVersions.length,
      averageOptimizationGain,
      mostImprovedContent,
      recentOptimizations
    };
  }

  /**
   * Clean up old versions (keep last N versions per content)
   */
  async cleanupOldVersions(keepVersions: number = 10): Promise<number> {
    let deletedCount = 0;

    for (const [contentId, versions] of this.versions.entries()) {
      if (versions.length > keepVersions) {
        // Sort by version number and keep the latest ones
        const sortedVersions = versions.sort((a, b) => b.version - a.version);
        const toKeep = sortedVersions.slice(0, keepVersions);
        const toDelete = sortedVersions.slice(keepVersions);

        this.versions.set(contentId, toKeep);
        deletedCount += toDelete.length;
      }
    }

    await this.saveVersionsToStorage();
    return deletedCount;
  }

  /**
   * Export version history for a content item
   */
  exportVersionHistory(contentId: string): string {
    const versions = this.getVersions(contentId);
    return JSON.stringify(versions, null, 2);
  }

  /**
   * Import version history from JSON
   */
  async importVersionHistory(contentId: string, jsonData: string): Promise<number> {
    try {
      const importedVersions: ContentVersion[] = JSON.parse(jsonData);
      
      // Validate imported data
      if (!Array.isArray(importedVersions)) {
        throw new Error('Invalid version data format');
      }

      // Convert date strings back to Date objects
      importedVersions.forEach(version => {
        version.createdAt = new Date(version.createdAt);
      });

      this.versions.set(contentId, importedVersions);
      await this.saveVersionsToStorage();
      
      return importedVersions.length;
    } catch (error) {
      throw new Error(`Failed to import version history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private calculateChanges(oldVersion: ContentVersion, newVersion: ContentVersion): ContentChange[] {
    const changes: ContentChange[] = [];

    // Title changes
    if (oldVersion.title !== newVersion.title) {
      changes.push({
        field: 'title',
        type: 'modified',
        oldValue: oldVersion.title,
        newValue: newVersion.title,
        impact: this.assessChangeImpact('title', oldVersion.title, newVersion.title),
        reason: 'Title optimization for better SEO performance'
      });
    }

    // Content changes
    if (oldVersion.content !== newVersion.content) {
      changes.push({
        field: 'content',
        type: 'modified',
        oldValue: oldVersion.content.substring(0, 100) + '...',
        newValue: newVersion.content.substring(0, 100) + '...',
        impact: this.assessChangeImpact('content', oldVersion.content, newVersion.content),
        reason: 'Content optimization for improved readability and SEO'
      });
    }

    // Meta description changes
    if (oldVersion.metaDescription !== newVersion.metaDescription) {
      changes.push({
        field: 'metaDescription',
        type: 'modified',
        oldValue: oldVersion.metaDescription,
        newValue: newVersion.metaDescription,
        impact: this.assessChangeImpact('metaDescription', oldVersion.metaDescription, newVersion.metaDescription),
        reason: 'Meta description optimization for better click-through rates'
      });
    }

    // Tags changes
    const oldTags = new Set(oldVersion.tags);
    const newTags = new Set(newVersion.tags);
    
    const addedTags = Array.from(newTags).filter(tag => !oldTags.has(tag));
    const removedTags = Array.from(oldTags).filter(tag => !newTags.has(tag));

    if (addedTags.length > 0) {
      changes.push({
        field: 'tags',
        type: 'added',
        oldValue: oldVersion.tags,
        newValue: addedTags,
        impact: 'medium',
        reason: 'Added new tags for better content categorization'
      });
    }

    if (removedTags.length > 0) {
      changes.push({
        field: 'tags',
        type: 'removed',
        oldValue: removedTags,
        newValue: newVersion.tags,
        impact: 'low',
        reason: 'Removed irrelevant tags for better focus'
      });
    }

    return changes;
  }

  private assessChangeImpact(field: string, oldValue: string, newValue: string): 'low' | 'medium' | 'high' {
    const lengthDiff = Math.abs(newValue.length - oldValue.length);
    const percentChange = lengthDiff / oldValue.length;

    if (field === 'title') {
      return percentChange > 0.3 ? 'high' : percentChange > 0.1 ? 'medium' : 'low';
    }

    if (field === 'metaDescription') {
      return percentChange > 0.2 ? 'high' : percentChange > 0.1 ? 'medium' : 'low';
    }

    if (field === 'content') {
      return percentChange > 0.1 ? 'high' : percentChange > 0.05 ? 'medium' : 'low';
    }

    return 'medium';
  }

  private analyzeImpact(oldVersion: ContentVersion, newVersion: ContentVersion, changes: ContentChange[]): ImpactAnalysis {
    const seoScoreChange = newVersion.seoScore - oldVersion.seoScore;
    const confidenceChange = newVersion.optimizationData.confidence - oldVersion.optimizationData.confidence;
    
    // Estimate traffic impact based on SEO score improvement
    const estimatedTrafficImpact = seoScoreChange * 2; // Rough estimate: 1 point = 2% traffic change

    const keywordImprovements = newVersion.optimizationData.focusKeywords
      .filter(keyword => !oldVersion.optimizationData.focusKeywords.includes(keyword));

    const potentialRisks: string[] = [];
    const recommendations: string[] = [];

    // Analyze risks
    if (seoScoreChange < 0) {
      potentialRisks.push('SEO score decreased - monitor performance closely');
    }

    const titleChange = changes.find(c => c.field === 'title');
    if (titleChange && titleChange.impact === 'high') {
      potentialRisks.push('Significant title change may affect existing rankings');
    }

    // Generate recommendations
    if (seoScoreChange > 0) {
      recommendations.push('Monitor organic traffic for positive impact');
    }

    if (keywordImprovements.length > 0) {
      recommendations.push(`Track rankings for new keywords: ${keywordImprovements.join(', ')}`);
    }

    return {
      seoScoreChange,
      confidenceChange,
      estimatedTrafficImpact,
      keywordImprovements,
      potentialRisks,
      recommendations
    };
  }

  private generateVersionId(): string {
    return `version_${Date.now()}_${++this.versionCounter}`;
  }

  private async saveVersionsToStorage(): Promise<void> {
    try {
      const data = JSON.stringify(Array.from(this.versions.entries()));
      localStorage.setItem('seo_content_versions', data);
    } catch (error) {
      console.error('Failed to save versions to storage:', error);
    }
  }

  private loadVersionsFromStorage(): void {
    try {
      const data = localStorage.getItem('seo_content_versions');
      if (data) {
        const entries = JSON.parse(data);
        this.versions = new Map(entries.map(([key, versions]: [string, any[]]) => [
          key,
          versions.map(v => ({
            ...v,
            createdAt: new Date(v.createdAt)
          }))
        ]));
      }
    } catch (error) {
      console.error('Failed to load versions from storage:', error);
      this.versions = new Map();
    }
  }
}

// Global instance
export const contentVersioningManager = new ContentVersioningManager();
