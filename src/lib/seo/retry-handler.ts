/**
 * <PERSON><PERSON> Handler with Exponential Backoff for SEO Operations
 * Provides intelligent retry logic for API failures
 */

export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  retryableStatusCodes: number[];
}

export interface RetryAttempt {
  attempt: number;
  delay: number;
  error?: Error;
  timestamp: Date;
}

export interface RetryStats {
  totalAttempts: number;
  successfulRetries: number;
  failedRetries: number;
  averageDelay: number;
  attempts: RetryAttempt[];
}

export class RetryHandler {
  private stats: RetryStats = {
    totalAttempts: 0,
    successfulRetries: 0,
    failedRetries: 0,
    averageDelay: 0,
    attempts: []
  };

  constructor(private config: RetryConfig) {}

  async execute<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      const attemptInfo: RetryAttempt = {
        attempt,
        delay: 0,
        timestamp: new Date()
      };

      try {
        if (attempt > 0) {
          const delay = this.calculateDelay(attempt);
          attemptInfo.delay = delay;
          
          console.log(`Retry attempt ${attempt}/${this.config.maxRetries} for ${context || 'operation'} after ${delay}ms`);
          await this.sleep(delay);
        }

        const result = await operation();
        
        if (attempt > 0) {
          this.stats.successfulRetries++;
          console.log(`Operation succeeded on retry attempt ${attempt}`);
        }

        this.stats.totalAttempts++;
        this.stats.attempts.push(attemptInfo);
        this.updateAverageDelay();
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        attemptInfo.error = lastError;
        this.stats.attempts.push(attemptInfo);

        if (!this.isRetryableError(lastError) || attempt === this.config.maxRetries) {
          this.stats.failedRetries++;
          this.stats.totalAttempts++;
          this.updateAverageDelay();
          break;
        }

        console.warn(`Attempt ${attempt + 1} failed: ${lastError.message}`);
      }
    }

    this.stats.failedRetries++;
    this.stats.totalAttempts++;
    this.updateAverageDelay();
    
    throw new Error(`Operation failed after ${this.config.maxRetries + 1} attempts. Last error: ${lastError!.message}`);
  }

  private calculateDelay(attempt: number): number {
    const exponentialDelay = this.config.initialDelay * Math.pow(this.config.backoffMultiplier, attempt - 1);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add jitter
    return Math.min(jitteredDelay, this.config.maxDelay);
  }

  private isRetryableError(error: Error): boolean {
    // Check error message
    const isRetryableMessage = this.config.retryableErrors.some(retryableError =>
      error.message.toLowerCase().includes(retryableError.toLowerCase())
    );

    // Check status codes if available
    const statusCode = this.extractStatusCode(error);
    const isRetryableStatus = statusCode ? this.config.retryableStatusCodes.includes(statusCode) : false;

    return isRetryableMessage || isRetryableStatus;
  }

  private extractStatusCode(error: Error): number | null {
    // Try to extract status code from error message
    const statusMatch = error.message.match(/status:?\s*(\d{3})/i);
    if (statusMatch) {
      return parseInt(statusMatch[1], 10);
    }

    // Check if error has a status property
    if ('status' in error && typeof error.status === 'number') {
      return error.status;
    }

    return null;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateAverageDelay(): void {
    const totalDelay = this.stats.attempts.reduce((sum, attempt) => sum + attempt.delay, 0);
    this.stats.averageDelay = this.stats.attempts.length > 0 ? totalDelay / this.stats.attempts.length : 0;
  }

  getStats(): RetryStats {
    return { ...this.stats };
  }

  reset(): void {
    this.stats = {
      totalAttempts: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageDelay: 0,
      attempts: []
    };
  }
}

// Default configuration for SEO operations
export const defaultSEORetryConfig: RetryConfig = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
  retryableErrors: [
    'timeout',
    'network error',
    'connection reset',
    'rate limit',
    'server error',
    'service unavailable',
    'internal server error'
  ],
  retryableStatusCodes: [429, 500, 502, 503, 504]
};
