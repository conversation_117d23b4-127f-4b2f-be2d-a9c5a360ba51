/**
 * Scheduled Content Generation Service
 * Automatically generates content on a schedule without human intervention
 */

import { automatedContentGenerator } from './automated-content-generator';
import { backgroundSEOService } from '../seo/background-seo-service';
import { supabase } from '@/integrations/supabase/client';

export interface ScheduleConfig {
  enabled: boolean;
  intervalHours: number;
  postsPerBatch: number;
  maxPostsPerDay: number;
  preferredTimes: string[]; // ['09:00', '15:00'] - times in HH:MM format
  weekdaysOnly: boolean;
  autoPublish: boolean;
}

export interface ContentSchedule {
  id: string;
  nextRun: Date;
  lastRun: Date | null;
  status: 'active' | 'paused' | 'error';
  config: ScheduleConfig;
  stats: {
    totalGenerated: number;
    successRate: number;
    lastError?: string;
  };
}

export class ScheduledContentService {
  private schedule: ContentSchedule | null = null;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  constructor() {
    this.loadScheduleConfig();
    this.startScheduler();
  }

  /**
   * Load schedule configuration from storage
   */
  private async loadScheduleConfig() {
    try {
      // In a real implementation, this would load from database
      // For now, using default configuration
      this.schedule = {
        id: 'default-schedule',
        nextRun: this.calculateNextRun({
          enabled: true,
          intervalHours: 24,
          postsPerBatch: 2,
          maxPostsPerDay: 5,
          preferredTimes: ['09:00', '15:00'],
          weekdaysOnly: true,
          autoPublish: false
        }),
        lastRun: null,
        status: 'active',
        config: {
          enabled: true,
          intervalHours: 24,
          postsPerBatch: 2,
          maxPostsPerDay: 5,
          preferredTimes: ['09:00', '15:00'],
          weekdaysOnly: true,
          autoPublish: false
        },
        stats: {
          totalGenerated: 0,
          successRate: 100
        }
      };

      console.log('📅 Content schedule loaded:', this.schedule);
    } catch (error) {
      console.error('❌ Failed to load schedule config:', error);
    }
  }

  /**
   * Start the content generation scheduler
   */
  private startScheduler() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    // Check every 5 minutes if it's time to generate content
    this.intervalId = setInterval(() => {
      this.checkAndRunSchedule();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('⏰ Content scheduler started');
  }

  /**
   * Stop the content generation scheduler
   */
  stopScheduler() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    console.log('⏹️ Content scheduler stopped');
  }

  /**
   * Check if it's time to run scheduled content generation
   */
  private async checkAndRunSchedule() {
    if (!this.schedule || !this.schedule.config.enabled || this.isRunning) {
      return;
    }

    const now = new Date();
    
    // Check if it's time to run
    if (now >= this.schedule.nextRun) {
      console.log('⏰ Time to generate scheduled content');
      await this.runScheduledGeneration();
    }
  }

  /**
   * Run scheduled content generation
   */
  private async runScheduledGeneration() {
    if (!this.schedule || this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.schedule.status = 'active';

    try {
      console.log('🚀 Starting scheduled content generation...');

      // Check daily limits
      const todayGenerated = await this.getTodayGeneratedCount();
      const remainingToday = this.schedule.config.maxPostsPerDay - todayGenerated;
      
      if (remainingToday <= 0) {
        console.log('📊 Daily content generation limit reached');
        this.schedule.nextRun = this.calculateNextRun(this.schedule.config);
        return;
      }

      const postsToGenerate = Math.min(this.schedule.config.postsPerBatch, remainingToday);

      // Generate content
      const generatedIds = await automatedContentGenerator.generateContentBatch(postsToGenerate);

      // Auto-publish if enabled
      if (this.schedule.config.autoPublish && generatedIds.length > 0) {
        await this.autoPublishContent(generatedIds);
      }

      // Queue for SEO optimization
      for (const contentId of generatedIds) {
        await backgroundSEOService.queueOptimization('blog_post', contentId, 1); // High priority
      }

      // Update stats
      this.schedule.stats.totalGenerated += generatedIds.length;
      this.schedule.lastRun = new Date();
      this.schedule.nextRun = this.calculateNextRun(this.schedule.config);

      console.log(`✅ Scheduled content generation completed: ${generatedIds.length} posts generated`);

      // Log to database for tracking
      await this.logScheduledRun(generatedIds.length, true);

    } catch (error) {
      console.error('❌ Scheduled content generation failed:', error);
      
      this.schedule.status = 'error';
      this.schedule.stats.lastError = error instanceof Error ? error.message : 'Unknown error';
      
      // Retry in 1 hour on error
      this.schedule.nextRun = new Date(Date.now() + 60 * 60 * 1000);

      await this.logScheduledRun(0, false, error instanceof Error ? error.message : 'Unknown error');
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Calculate next run time based on configuration
   */
  private calculateNextRun(config: ScheduleConfig): Date {
    const now = new Date();
    let nextRun = new Date(now.getTime() + config.intervalHours * 60 * 60 * 1000);

    // If preferred times are set, adjust to next preferred time
    if (config.preferredTimes.length > 0) {
      const currentTime = now.getHours() * 100 + now.getMinutes();
      
      for (const timeStr of config.preferredTimes) {
        const [hours, minutes] = timeStr.split(':').map(Number);
        const preferredTime = hours * 100 + minutes;
        
        if (preferredTime > currentTime) {
          nextRun = new Date(now);
          nextRun.setHours(hours, minutes, 0, 0);
          break;
        }
      }
      
      // If no preferred time today, use first preferred time tomorrow
      if (nextRun <= now) {
        const [hours, minutes] = config.preferredTimes[0].split(':').map(Number);
        nextRun = new Date(now);
        nextRun.setDate(nextRun.getDate() + 1);
        nextRun.setHours(hours, minutes, 0, 0);
      }
    }

    // Skip weekends if weekdaysOnly is enabled
    if (config.weekdaysOnly) {
      while (nextRun.getDay() === 0 || nextRun.getDay() === 6) {
        nextRun.setDate(nextRun.getDate() + 1);
      }
    }

    return nextRun;
  }

  /**
   * Get count of content generated today
   */
  private async getTodayGeneratedCount(): Promise<number> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const { count, error } = await supabase
        .from('blog_posts')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Failed to get today generated count:', error);
      return 0;
    }
  }

  /**
   * Auto-publish generated content
   */
  private async autoPublishContent(contentIds: string[]) {
    try {
      console.log('📢 Auto-publishing generated content...');

      const { error } = await supabase
        .from('blog_posts')
        .update({ published: true })
        .in('id', contentIds);

      if (error) throw error;

      console.log(`✅ Auto-published ${contentIds.length} posts`);
    } catch (error) {
      console.error('❌ Auto-publish failed:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Log scheduled run for tracking
   */
  private async logScheduledRun(postsGenerated: number, success: boolean, error?: string) {
    try {
      // In a real implementation, this would log to a dedicated table
      console.log('📝 Logging scheduled run:', {
        timestamp: new Date().toISOString(),
        postsGenerated,
        success,
        error
      });
    } catch (logError) {
      console.error('Failed to log scheduled run:', logError);
    }
  }

  /**
   * Update schedule configuration
   */
  async updateScheduleConfig(config: Partial<ScheduleConfig>) {
    if (!this.schedule) return;

    this.schedule.config = { ...this.schedule.config, ...config };
    this.schedule.nextRun = this.calculateNextRun(this.schedule.config);

    console.log('⚙️ Schedule configuration updated:', this.schedule.config);

    // Restart scheduler with new config
    this.startScheduler();
  }

  /**
   * Get current schedule status
   */
  getScheduleStatus(): ContentSchedule | null {
    return this.schedule;
  }

  /**
   * Manually trigger content generation
   */
  async triggerManualGeneration(postsCount: number = 1): Promise<string[]> {
    if (this.isRunning) {
      throw new Error('Content generation already in progress');
    }

    console.log(`🎯 Manual content generation triggered: ${postsCount} posts`);

    try {
      const generatedIds = await automatedContentGenerator.generateContentBatch(postsCount);

      // Queue for SEO optimization
      for (const contentId of generatedIds) {
        await backgroundSEOService.queueOptimization('blog_post', contentId, 1);
      }

      if (this.schedule) {
        this.schedule.stats.totalGenerated += generatedIds.length;
      }

      return generatedIds;
    } catch (error) {
      console.error('❌ Manual content generation failed:', error);
      throw error;
    }
  }

  /**
   * Pause scheduled content generation
   */
  pauseSchedule() {
    if (this.schedule) {
      this.schedule.status = 'paused';
      this.schedule.config.enabled = false;
      console.log('⏸️ Content generation schedule paused');
    }
  }

  /**
   * Resume scheduled content generation
   */
  resumeSchedule() {
    if (this.schedule) {
      this.schedule.status = 'active';
      this.schedule.config.enabled = true;
      this.schedule.nextRun = this.calculateNextRun(this.schedule.config);
      console.log('▶️ Content generation schedule resumed');
    }
  }

  /**
   * Get content generation analytics
   */
  async getAnalytics(): Promise<{
    totalGenerated: number;
    generatedThisWeek: number;
    generatedThisMonth: number;
    averagePerDay: number;
    successRate: number;
    nextScheduledRun: Date | null;
  }> {
    try {
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [totalResult, weekResult, monthResult] = await Promise.all([
        supabase.from('blog_posts').select('*', { count: 'exact', head: true }),
        supabase.from('blog_posts').select('*', { count: 'exact', head: true }).gte('created_at', weekAgo.toISOString()),
        supabase.from('blog_posts').select('*', { count: 'exact', head: true }).gte('created_at', monthAgo.toISOString())
      ]);

      const totalGenerated = totalResult.count || 0;
      const generatedThisWeek = weekResult.count || 0;
      const generatedThisMonth = monthResult.count || 0;
      const averagePerDay = generatedThisMonth / 30;

      return {
        totalGenerated,
        generatedThisWeek,
        generatedThisMonth,
        averagePerDay,
        successRate: this.schedule?.stats.successRate || 0,
        nextScheduledRun: this.schedule?.nextRun || null
      };
    } catch (error) {
      console.error('Failed to get analytics:', error);
      return {
        totalGenerated: 0,
        generatedThisWeek: 0,
        generatedThisMonth: 0,
        averagePerDay: 0,
        successRate: 0,
        nextScheduledRun: null
      };
    }
  }
}

// Export singleton instance
export const scheduledContentService = new ScheduledContentService();
