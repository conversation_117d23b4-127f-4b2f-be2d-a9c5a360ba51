export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_users: {
        Row: {
          created_at: string | null
          email: string
          id: string
          role: Database["public"]["Enums"]["admin_role"] | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          role?: Database["public"]["Enums"]["admin_role"] | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          role?: Database["public"]["Enums"]["admin_role"] | null
          user_id?: string | null
        }
        Relationships: []
      }
      blog_posts: {
        Row: {
          content: string
          cover_image: string | null
          created_at: string | null
          excerpt: string
          id: string
          last_optimized_at: string | null
          meta_description: string | null
          optimization_count: number | null
          optimized_title: string | null
          published: boolean | null
          seo_tags: string[] | null
          slug: string
          tags: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          content: string
          cover_image?: string | null
          created_at?: string | null
          excerpt: string
          id?: string
          last_optimized_at?: string | null
          meta_description?: string | null
          optimization_count?: number | null
          optimized_title?: string | null
          published?: boolean | null
          seo_tags?: string[] | null
          slug: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          content?: string
          cover_image?: string | null
          created_at?: string | null
          excerpt?: string
          id?: string
          last_optimized_at?: string | null
          meta_description?: string | null
          optimization_count?: number | null
          optimized_title?: string | null
          published?: boolean | null
          seo_tags?: string[] | null
          slug?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      booking_inquiries: {
        Row: {
          created_at: string | null
          email: string
          id: string
          message: string | null
          name: string
          phone: string
          preferred_dates: string | null
          status: Database["public"]["Enums"]["booking_status"] | null
          trek_name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          message?: string | null
          name: string
          phone: string
          preferred_dates?: string | null
          status?: Database["public"]["Enums"]["booking_status"] | null
          trek_name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          message?: string | null
          name?: string
          phone?: string
          preferred_dates?: string | null
          status?: Database["public"]["Enums"]["booking_status"] | null
          trek_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      seo_data: {
        Row: {
          created_at: string | null
          description: string
          id: string
          keywords: string[] | null
          page_type: string
          schema_json: Json | null
          slug: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          id?: string
          keywords?: string[] | null
          page_type: string
          schema_json?: Json | null
          slug?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          id?: string
          keywords?: string[] | null
          page_type?: string
          schema_json?: Json | null
          slug?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      seo_optimization_logs: {
        Row: {
          blog_post_id: string | null
          created_at: string | null
          error_message: string | null
          gemini_response: Json | null
          id: string
          optimization_type: string
          optimized_content: string | null
          optimized_meta_description: string | null
          optimized_tags: string[] | null
          optimized_title: string | null
          original_content: string | null
          original_meta_description: string | null
          original_tags: string[] | null
          original_title: string | null
          processing_time_ms: number | null
          status: string
        }
        Insert: {
          blog_post_id?: string | null
          created_at?: string | null
          error_message?: string | null
          gemini_response?: Json | null
          id?: string
          optimization_type: string
          optimized_content?: string | null
          optimized_meta_description?: string | null
          optimized_tags?: string[] | null
          optimized_title?: string | null
          original_content?: string | null
          original_meta_description?: string | null
          original_tags?: string[] | null
          original_title?: string | null
          processing_time_ms?: number | null
          status: string
        }
        Update: {
          blog_post_id?: string | null
          created_at?: string | null
          error_message?: string | null
          gemini_response?: Json | null
          id?: string
          optimization_type?: string
          optimized_content?: string | null
          optimized_meta_description?: string | null
          optimized_tags?: string[] | null
          optimized_title?: string | null
          original_content?: string | null
          original_meta_description?: string | null
          original_tags?: string[] | null
          original_title?: string | null
          processing_time_ms?: number | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "seo_optimization_logs_blog_post_id_fkey"
            columns: ["blog_post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      testimonials: {
        Row: {
          avatar: string | null
          country: string
          created_at: string | null
          id: string
          name: string
          quote: string
          rating: number | null
          trek_name: string | null
        }
        Insert: {
          avatar?: string | null
          country: string
          created_at?: string | null
          id?: string
          name: string
          quote: string
          rating?: number | null
          trek_name?: string | null
        }
        Update: {
          avatar?: string | null
          country?: string
          created_at?: string | null
          id?: string
          name?: string
          quote?: string
          rating?: number | null
          trek_name?: string | null
        }
        Relationships: []
      }
      trek_packages: {
        Row: {
          best_season: string | null
          created_at: string | null
          difficulty_level: string | null
          duration_days: number | null
          featured: boolean | null
          gallery: string[] | null
          id: string
          itinerary: Json
          long_description: string
          max_altitude: number | null
          name: string
          price_usd: number | null
          region: string
          short_description: string
          slug: string
          updated_at: string | null
        }
        Insert: {
          best_season?: string | null
          created_at?: string | null
          difficulty_level?: string | null
          duration_days?: number | null
          featured?: boolean | null
          gallery?: string[] | null
          id?: string
          itinerary?: Json
          long_description: string
          max_altitude?: number | null
          name: string
          price_usd?: number | null
          region: string
          short_description: string
          slug: string
          updated_at?: string | null
        }
        Update: {
          best_season?: string | null
          created_at?: string | null
          difficulty_level?: string | null
          duration_days?: number | null
          featured?: boolean | null
          gallery?: string[] | null
          id?: string
          itinerary?: Json
          long_description?: string
          max_altitude?: number | null
          name?: string
          price_usd?: number | null
          region?: string
          short_description?: string
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      invoke_seo_optimizer: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      admin_role: "admin" | "editor"
      booking_status: "pending" | "contacted" | "confirmed" | "cancelled"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      admin_role: ["admin", "editor"],
      booking_status: ["pending", "contacted", "confirmed", "cancelled"],
    },
  },
} as const
