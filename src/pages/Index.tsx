
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import HeroSection from '@/components/HeroSection';
import AboutSection from '@/components/AboutSection';
import FeaturedTreks from '@/components/FeaturedTreks';
import TestimonialsSection from '@/components/TestimonialsSection';
import BlogPreview from '@/components/BlogPreview';
import ContactSection from '@/components/ContactSection';
import PageSEO from '@/components/SEO/PageSEO';
import { seoConfigs } from '@/data/seoConfig';

const Index = () => {
  return (
    <div className="min-h-screen">
      <PageSEO
        title={seoConfigs.home.title}
        description={seoConfigs.home.description}
        keywords={seoConfigs.home.keywords}
        structuredData={seoConfigs.home.structuredData}
      />
      <Header />
      <main>
        <HeroSection />
        <AboutSection />
        <FeaturedTreks />
        <TestimonialsSection />
        <BlogPreview />
        <ContactSection />
      </main>
      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default Index;
