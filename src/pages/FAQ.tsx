import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, Mountain, Shield, Calendar, DollarSign } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import PageSEO from '@/components/SEO/PageSEO';
import { seoConfigs } from '@/data/seoConfig';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: 'permits' | 'preparation' | 'safety' | 'booking' | 'general';
  icon: React.ReactNode;
}

const FAQ = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const faqData: FAQItem[] = [
    {
      id: 1,
      category: 'permits',
      icon: <Shield className="h-5 w-5 text-blue-600" />,
      question: "What permits do I need for trekking in Nepal?",
      answer: "You need TIMS (Trekkers' Information Management System) card and National Park entry permits for most treks in Nepal. For Everest region, you need Sagarmatha National Park permit. For Annapurna region, you need ACAP (Annapurna Conservation Area Project) permit. We handle all permit arrangements for our clients."
    },
    {
      id: 2,
      category: 'preparation',
      icon: <Mountain className="h-5 w-5 text-green-600" />,
      question: "What is the best time to trek in Nepal?",
      answer: "The best times are pre-monsoon (March-May) and post-monsoon (September-November) seasons. Spring offers rhododendron blooms and clear mountain views, while autumn provides the clearest skies and stable weather. Winter treks are possible at lower altitudes, and monsoon season is suitable for rain-shadow areas like Upper Mustang."
    },
    {
      id: 3,
      category: 'preparation',
      icon: <Mountain className="h-5 w-5 text-green-600" />,
      question: "How fit do I need to be for Nepal trekking?",
      answer: "Fitness requirements vary by trek. For moderate treks like Annapurna Circuit, good cardiovascular fitness and ability to walk 5-7 hours daily is sufficient. For challenging treks like Everest Base Camp, 3-6 months of regular cardio training, hiking with a backpack, and stair climbing is recommended. We provide detailed fitness guidelines for each trek."
    },
    {
      id: 4,
      category: 'safety',
      icon: <Shield className="h-5 w-5 text-red-600" />,
      question: "How do you prevent and treat altitude sickness?",
      answer: "We follow strict acclimatization schedules with rest days at key altitudes. Our guides are trained in altitude sickness recognition and treatment. We carry oxygen, pulse oximeters, and first aid supplies. Gradual ascent, proper hydration, and listening to your body are key. Helicopter evacuation is available for serious cases."
    },
    {
      id: 5,
      category: 'booking',
      icon: <DollarSign className="h-5 w-5 text-orange-600" />,
      question: "What's included in your trek packages?",
      answer: "Our packages include accommodation (teahouses/lodges), all meals during trek, experienced guide and porter services, permits and fees, airport transfers, and emergency support. International flights, Nepal visa, travel insurance, personal gear, and drinks are not included. Detailed inclusions/exclusions are provided for each trek."
    },
    {
      id: 6,
      category: 'general',
      icon: <HelpCircle className="h-5 w-5 text-purple-600" />,
      question: "Can I trek solo or do I need a guide?",
      answer: "While independent trekking is possible on main routes, we strongly recommend guided treks for safety, cultural insights, and navigation. Solo trekking requires permits, experience, and emergency preparedness. Our guides provide local knowledge, handle logistics, and ensure your safety throughout the journey."
    },
    {
      id: 7,
      category: 'preparation',
      icon: <Mountain className="h-5 w-5 text-green-600" />,
      question: "What gear do I need for Nepal trekking?",
      answer: "Essential gear includes good trekking boots, warm layers, rain gear, sleeping bag (rated for expected temperatures), daypack, and personal items. We provide detailed packing lists for each trek. Gear rental is available in Kathmandu for items like sleeping bags, down jackets, and trekking poles."
    },
    {
      id: 8,
      category: 'safety',
      icon: <Shield className="h-5 w-5 text-red-600" />,
      question: "Do I need travel insurance for Nepal trekking?",
      answer: "Yes, comprehensive travel insurance including helicopter evacuation coverage up to 6000m is mandatory. Your insurance should cover adventure activities, medical expenses, trip cancellation, and emergency evacuation. We can recommend suitable insurance providers that cover Nepal trekking activities."
    },
    {
      id: 9,
      category: 'general',
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      question: "How far in advance should I book my trek?",
      answer: "We recommend booking 2-3 months in advance, especially for peak seasons (March-May, September-November). This allows time for permit processing, fitness preparation, and gear acquisition. Last-minute bookings are possible but may have limited availability and higher costs."
    },
    {
      id: 10,
      category: 'booking',
      icon: <DollarSign className="h-5 w-5 text-orange-600" />,
      question: "What are your payment terms and cancellation policy?",
      answer: "We require 25% deposit to confirm booking, with balance due 30 days before departure. Cancellations more than 30 days before departure incur 25% penalty. Cancellations within 30 days forfeit full payment unless due to medical reasons with documentation. We offer flexible rescheduling options."
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions', count: faqData.length },
    { id: 'permits', name: 'Permits & Documentation', count: faqData.filter(item => item.category === 'permits').length },
    { id: 'preparation', name: 'Trek Preparation', count: faqData.filter(item => item.category === 'preparation').length },
    { id: 'safety', name: 'Safety & Health', count: faqData.filter(item => item.category === 'safety').length },
    { id: 'booking', name: 'Booking & Payments', count: faqData.filter(item => item.category === 'booking').length },
    { id: 'general', name: 'General Questions', count: faqData.filter(item => item.category === 'general').length }
  ];

  const filteredFAQs = selectedCategory === 'all' 
    ? faqData 
    : faqData.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen">
      <PageSEO 
        title={seoConfigs.faq.title}
        description={seoConfigs.faq.description}
        keywords={seoConfigs.faq.keywords}
        structuredData={seoConfigs.faq.structuredData}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="pt-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl sm:text-5xl font-bold mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Frequently Asked Questions About Nepal Trekking
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Get answers to common questions about trekking in Nepal. From permits to preparation, we've got you covered.
          </p>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-blue-50'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-4">
              {filteredFAQs.map((item) => (
                <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <button
                    onClick={() => toggleItem(item.id)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      {item.icon}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {item.question}
                      </h3>
                    </div>
                    {openItems.includes(item.id) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                  
                  {openItems.includes(item.id) && (
                    <div className="px-6 pb-4">
                      <div className="pl-8">
                        <p className="text-gray-700 leading-relaxed">
                          {item.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Still Have Questions?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Our expert team is here to help you plan the perfect Nepal trekking adventure. Get in touch for personalized advice.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
            >
              Contact Our Experts
            </a>
            <button
              onClick={() => {
                const message = "Hi! I have questions about Nepal trekking. Can you help me?";
                window.open(`https://wa.me/+9779841234567?text=${encodeURIComponent(message)}`, '_blank');
              }}
              className="inline-flex items-center px-6 py-3 bg-green-500 text-white font-semibold rounded-lg hover:bg-green-600 transition-colors"
            >
              WhatsApp Us
            </button>
          </div>
        </div>
      </section>

      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default FAQ;
