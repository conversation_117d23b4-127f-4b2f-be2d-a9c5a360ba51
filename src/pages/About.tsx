
import React from 'react';
import { Mountain, Users, Award, Heart, Shield, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import { Link } from 'react-router-dom';
import PageSEO from '@/components/SEO/PageSEO';
import { seoConfigs } from '@/data/seoConfig';

const About = () => {
  const values = [
    {
      icon: <Mountain className="h-8 w-8 text-blue-600" />,
      title: "Local Expertise",
      description: "Our experienced local guides know every trail, ensuring your safety and enriching your journey with authentic cultural insights."
    },
    {
      icon: <Shield className="h-8 w-8 text-green-600" />,
      title: "Safety First",
      description: "We prioritize your safety with comprehensive insurance, emergency protocols, and well-maintained equipment for every expedition."
    },
    {
      icon: <Heart className="h-8 w-8 text-orange-500" />,
      title: "Community Impact",
      description: "We support local communities by hiring locally, staying in family-run lodges, and contributing to conservation efforts."
    },
    {
      icon: <Globe className="h-8 w-8 text-purple-600" />,
      title: "Sustainable Tourism",
      description: "We practice responsible tourism, minimizing environmental impact while maximizing positive contributions to local communities."
    }
  ];

  const team = [
    {
      name: "Pemba Sherpa",
      role: "Founder & Lead Guide",
      experience: "15+ years",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",
      description: "Born in the Khumbu region, Pemba has summited Everest 8 times and led over 500 successful treks."
    },
    {
      name: "Maya Tamang",
      role: "Operations Manager",
      experience: "10+ years",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
      description: "Maya coordinates all logistics with precision, ensuring seamless experiences for our trekkers."
    },
    {
      name: "Ang Dorje",
      role: "Senior Guide",
      experience: "12+ years",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
      description: "Specializing in high-altitude treks, Ang Dorje brings deep knowledge of mountain safety and weather patterns."
    }
  ];

  return (
    <div className="min-h-screen">
      <PageSEO
        title={seoConfigs.about.title}
        description={seoConfigs.about.description}
        keywords={seoConfigs.about.keywords}
        structuredData={seoConfigs.about.structuredData}
      />
      <Header />

      {/* Hero Section */}
      <section className="pt-16 relative h-96 bg-gray-900">
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=600&fit=crop')`
          }}
        />
        <div className="relative z-10 container mx-auto px-4 h-full flex items-center">
          <div className="text-white max-w-3xl">
            <h1 className="text-4xl sm:text-5xl font-bold mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
              About TrekNepalX - Your Trusted Nepal Trekking Partner
            </h1>
            <p className="text-xl text-gray-200">
              Your trusted partner for authentic Himalayan adventures, connecting you with Nepal's majestic mountains and rich culture.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6" style={{ fontFamily: 'DM Serif Display, serif' }}>
                Our Story - From Local Guides to Nepal's Premier Trekking Company
              </h2>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  Founded in 2010 by local mountain guides, TrekNepalX was born from a passion for sharing the incredible beauty and culture of Nepal with the world. What started as a small team of dedicated Sherpa guides has grown into one of Nepal's most trusted trekking companies.
                </p>
                <p>
                  We believe that the best way to experience the Himalayas is through the eyes of those who call these mountains home. Our team consists entirely of local guides who have grown up in the shadow of the world's highest peaks, bringing generations of mountain wisdom to every trek.
                </p>
                <p>
                  Over the years, we've successfully guided thousands of adventurers from around the globe, always prioritizing safety, authenticity, and respect for the natural environment and local communities that make our work possible.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=600&h=400&fit=crop"
                alt="Mountain landscape with prayer flags"
                className="rounded-lg shadow-lg"
              />
              <div className="absolute -bottom-6 -right-6 bg-blue-600 text-white p-6 rounded-lg shadow-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold">5000+</div>
                  <div className="text-sm">Happy Trekkers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
              Our Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These core principles guide everything we do, from planning your trek to supporting local communities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="flex justify-center mb-4">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our experienced team of local guides and support staff are the heart of TrekNepalX.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-64 bg-gray-200">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {member.name}
                  </h3>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-blue-600 font-medium">{member.role}</span>
                    <span className="text-sm text-gray-500">{member.experience}</span>
                  </div>
                  <p className="text-gray-600">
                    {member.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-orange-500 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">5000+</div>
              <div className="text-lg">Happy Trekkers</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-lg">Trek Routes</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">15+</div>
              <div className="text-lg">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">25+</div>
              <div className="text-lg">Expert Guides</div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Ready to Start Your Adventure?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied trekkers who have discovered the magic of Nepal with TrekNepalX.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/treks">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Explore Trek Packages
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline">
                Get in Touch
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default About;
