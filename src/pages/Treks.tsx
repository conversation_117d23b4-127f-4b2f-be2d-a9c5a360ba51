
import React, { useState, useEffect } from 'react';
import { Clock, Users, TrendingUp, MapPin, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { supabase } from '@/integrations/supabase/client';
import { TrekPackage } from '@/types/database';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import BookingModal from '@/components/BookingModal';
import { Link } from 'react-router-dom';
import PageSEO from '@/components/SEO/PageSEO';
import { seoConfigs } from '@/data/seoConfig';

const Treks = () => {
  const [treks, setTreks] = useState<TrekPackage[]>([]);
  const [filteredTreks, setFilteredTreks] = useState<TrekPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedTrek, setSelectedTrek] = useState<string>('');
  const [isBookingOpen, setIsBookingOpen] = useState(false);

  useEffect(() => {
    fetchTreks();
  }, []);

  useEffect(() => {
    filterTreks();
  }, [treks, searchTerm, selectedRegion, selectedDifficulty]);

  const fetchTreks = async () => {
    try {
      const { data, error } = await supabase
        .from('trek_packages')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const transformedTreks = data?.map(trek => ({
        ...trek,
        itinerary: Array.isArray(trek.itinerary) ? trek.itinerary : []
      })) as TrekPackage[] || [];

      setTreks(transformedTreks);
    } catch (error) {
      console.error('Error fetching treks:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterTreks = () => {
    let filtered = treks;

    if (searchTerm) {
      filtered = filtered.filter(trek =>
        trek.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trek.region.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trek.short_description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedRegion !== 'all') {
      filtered = filtered.filter(trek => trek.region === selectedRegion);
    }

    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(trek => trek.difficulty_level === selectedDifficulty);
    }

    setFilteredTreks(filtered);
  };

  const regions = [...new Set(treks.map(trek => trek.region))];
  const difficulties = [...new Set(treks.map(trek => trek.difficulty_level).filter(Boolean))];

  const handleBookNow = (trekName: string) => {
    setSelectedTrek(trekName);
    setIsBookingOpen(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <PageSEO
        title={seoConfigs.treks.title}
        description={seoConfigs.treks.description}
        keywords={seoConfigs.treks.keywords}
        structuredData={seoConfigs.treks.structuredData}
      />
      <Header />

      {/* Hero Section */}
      <section className="pt-16 bg-gradient-to-r from-blue-600 to-orange-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl sm:text-5xl font-bold mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
            Trek Packages
          </h1>
          <p className="text-xl max-w-2xl mx-auto">
            Discover our carefully curated collection of trekking adventures across Nepal's magnificent landscapes
          </p>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search treks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={selectedRegion}
              onChange={(e) => setSelectedRegion(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Regions</option>
              {regions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>

            <select
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Difficulties</option>
              {difficulties.map(difficulty => (
                <option key={difficulty} value={difficulty}>{difficulty}</option>
              ))}
            </select>

            <div className="flex items-center text-gray-600">
              <Filter className="h-4 w-4 mr-2" />
              <span>{filteredTreks.length} trek{filteredTreks.length !== 1 ? 's' : ''} found</span>
            </div>
          </div>
        </div>
      </section>

      {/* Treks Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredTreks.map((trek) => (
              <div key={trek.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48 bg-gray-200">
                  <img
                    src={`https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80`}
                    alt={trek.name}
                    className="w-full h-full object-cover"
                  />
                  {trek.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Featured
                      </span>
                    </div>
                  )}
                  {trek.price_usd && (
                    <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                      <span className="text-lg font-bold text-gray-900">${trek.price_usd}</span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-blue-600 font-medium">{trek.region}</span>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-600">{trek.max_altitude}m</span>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {trek.name}
                  </h3>

                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {trek.short_description}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{trek.duration_days} days</span>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      <span>{trek.difficulty_level}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      <span>2-12 people</span>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Link to={`/treks/${trek.slug}`} className="flex-1">
                      <Button variant="outline" className="w-full">
                        Learn More
                      </Button>
                    </Link>
                    <Button
                      onClick={() => handleBookNow(trek.name)}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      Book Now
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredTreks.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No treks found</h3>
              <p className="text-gray-600">Try adjusting your search criteria or browse all our available treks.</p>
            </div>
          )}
        </div>
      </section>

      <Footer />
      <WhatsAppButton />

      <BookingModal
        isOpen={isBookingOpen}
        onClose={() => setIsBookingOpen(false)}
        trekName={selectedTrek}
      />
    </div>
  );
};

export default Treks;
