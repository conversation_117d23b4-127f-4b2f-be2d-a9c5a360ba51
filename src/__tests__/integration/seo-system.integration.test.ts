/**
 * Integration Tests for Enhanced SEO System
 * End-to-end testing of the complete SEO optimization workflow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedSEOOptimizer } from '@/lib/seo/enhanced-optimizer';
import { contentVersioningManager } from '@/lib/seo/content-versioning';
import EnhancedBlogFormSEOPanel from '@/components/admin/EnhancedBlogFormSEOPanel';

// Mock fetch globally
global.fetch = vi.fn();

// Mock environment variables
vi.stubEnv('VITE_GEMINI_API_KEY', 'test-api-key');

describe('SEO System Integration Tests', () => {
  const mockGeminiResponse = {
    candidates: [{
      content: {
        parts: [{
          text: JSON.stringify({
            optimizedTitle: 'Ultimate Guide to Nepal Trekking Adventures',
            optimizedMetaDescription: 'Discover the best trekking routes in Nepal with our comprehensive guide. Expert tips, safety advice, and stunning destinations await.',
            focusKeywords: ['nepal trekking', 'himalayan adventures', 'mountain climbing'],
            contentSuggestions: [
              {
                section: 'Introduction',
                currentText: 'Nepal is a great place for trekking.',
                suggestedText: 'Nepal offers some of the world\'s most spectacular trekking experiences in the heart of the Himalayas.',
                reason: 'More engaging and descriptive opening',
                impact: 'high'
              }
            ],
            technicalRecommendations: [
              'Add internal links to related trekking guides',
              'Include altitude sickness prevention tips',
              'Add seasonal trekking calendar'
            ],
            optimizationConfidence: 9,
            estimatedImpact: 'high'
          })
        }]
      }
    }]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });
  });

  afterEach(() => {
    vi.unstubAllEnvs();
  });

  describe('Complete Optimization Workflow', () => {
    it('should perform end-to-end content optimization', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key'
      });

      const request = {
        content: `
          Nepal is a great place for trekking. The mountains are very high and beautiful.
          Many people come here every year to climb and trek. The weather can be challenging
          but the views are worth it. You should prepare well before coming.
        `,
        title: 'Nepal Trekking',
        excerpt: 'A guide to trekking in Nepal',
        tags: ['nepal', 'trekking', 'mountains'],
        contentType: 'blog' as const
      };

      const result = await optimizer.optimizeContent(request);

      // Verify optimization results
      expect(result.optimizedTitle).toBe('Ultimate Guide to Nepal Trekking Adventures');
      expect(result.optimizedMetaDescription).toContain('Discover the best trekking routes');
      expect(result.focusKeywords).toContain('nepal trekking');
      expect(result.contentSuggestions).toHaveLength(1);
      expect(result.technicalRecommendations).toHaveLength(3);
      expect(result.optimizationConfidence).toBe(9);
      expect(result.estimatedImpact).toBe('high');

      // Verify API call was made correctly
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('generativelanguage.googleapis.com'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('Nepal is a great place for trekking')
        })
      );

      // Verify statistics tracking
      const stats = optimizer.getStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.successfulOptimizations).toBe(1);
      expect(stats.failedOptimizations).toBe(0);

      optimizer.destroy();
    });

    it('should handle optimization with versioning', async () => {
      const contentId = 'test-blog-post-123';
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key'
      });

      // Create initial version
      const initialVersion = await contentVersioningManager.createVersion(
        contentId,
        'blog',
        {
          title: 'Nepal Trekking',
          content: 'Basic content about Nepal trekking.',
          metaDescription: 'A guide to trekking in Nepal',
          tags: ['nepal', 'trekking'],
          seoScore: 6.0,
          optimizationData: {
            optimizationType: 'manual',
            confidence: 5,
            estimatedImpact: 'medium',
            focusKeywords: ['nepal', 'trekking'],
            technicalRecommendations: [],
            processingTime: 0,
            aiModel: 'manual',
            optimizerVersion: '1.0.0'
          },
          createdBy: 'test-user'
        }
      );

      // Perform optimization
      const request = {
        content: 'Enhanced content about Nepal trekking adventures.',
        title: 'Ultimate Guide to Nepal Trekking Adventures',
        excerpt: 'Discover the best trekking routes in Nepal with our comprehensive guide.',
        tags: ['nepal', 'trekking', 'adventures'],
        contentType: 'blog' as const
      };

      const optimizationResult = await optimizer.optimizeContent(request);

      // Create optimized version
      const optimizedVersion = await contentVersioningManager.createVersion(
        contentId,
        'blog',
        {
          title: optimizationResult.optimizedTitle,
          content: request.content,
          metaDescription: optimizationResult.optimizedMetaDescription,
          tags: optimizationResult.focusKeywords,
          seoScore: optimizationResult.optimizationConfidence,
          optimizationData: {
            optimizationType: 'ai-enhanced',
            confidence: optimizationResult.optimizationConfidence,
            estimatedImpact: optimizationResult.estimatedImpact,
            focusKeywords: optimizationResult.focusKeywords,
            technicalRecommendations: optimizationResult.technicalRecommendations,
            processingTime: optimizationResult.processingTime,
            aiModel: 'gemini-pro',
            optimizerVersion: optimizationResult.version
          },
          createdBy: 'ai-optimizer',
          parentVersionId: initialVersion.id
        }
      );

      // Verify version comparison
      const comparison = contentVersioningManager.compareVersions(
        initialVersion.id,
        optimizedVersion.id
      );

      expect(comparison).toBeDefined();
      expect(comparison!.impactAnalysis.seoScoreChange).toBe(3); // 9 - 6
      expect(comparison!.changes.some(c => c.field === 'title')).toBe(true);

      // Verify optimization history
      const history = contentVersioningManager.getOptimizationHistory(contentId);
      expect(history).toHaveLength(1);
      expect(history[0].optimizationData.optimizationType).toBe('ai-enhanced');

      optimizer.destroy();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle API failures with circuit breaker', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key',
        circuitBreaker: {
          failureThreshold: 2,
          resetTimeout: 1000,
          monitoringPeriod: 5000,
          expectedErrors: []
        }
      });

      // Mock API failures
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const request = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog' as const
      };

      // First two requests should fail and open circuit breaker
      await expect(optimizer.optimizeContent(request)).rejects.toThrow();
      await expect(optimizer.optimizeContent(request)).rejects.toThrow();

      // Third request should fail immediately due to open circuit breaker
      await expect(optimizer.optimizeContent(request)).rejects.toThrow('Circuit breaker is OPEN');

      // Verify circuit breaker state
      const health = await optimizer.healthCheck();
      expect(health.status).toBe('unhealthy');

      optimizer.destroy();
    });

    it('should use fallback optimization when API is unavailable', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key',
        enableFallback: true
      });

      // Mock rate limit error
      (global.fetch as any).mockResolvedValue({
        ok: false,
        status: 429,
        text: () => Promise.resolve('Rate limit exceeded')
      });

      const request = {
        content: 'Test content about Nepal trekking adventures.',
        title: 'Very Long Title That Exceeds The Recommended Length For SEO Optimization And Should Be Truncated',
        excerpt: 'Test excerpt',
        tags: ['nepal', 'trekking'],
        contentType: 'blog' as const
      };

      const result = await optimizer.optimizeContent(request);

      // Verify fallback optimization
      expect(result.optimizedTitle).toContain('...');
      expect(result.optimizationConfidence).toBe(3);
      expect(result.estimatedImpact).toBe('low');
      expect(result.version).toBe('2.0.0-fallback');
      expect(result.technicalRecommendations).toContain('AI optimization temporarily unavailable');

      optimizer.destroy();
    });

    it('should handle rate limiting gracefully', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key',
        rateLimit: {
          maxTokens: 1,
          refillRate: 0.5, // Very slow refill for testing
          refillInterval: 100,
          queueSize: 2,
          requestTimeout: 1000
        }
      });

      const request = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog' as const
      };

      // First request should succeed immediately
      const promise1 = optimizer.optimizeContent(request);
      
      // Second request should be queued
      const promise2 = optimizer.optimizeContent(request);
      
      // Third request should be queued
      const promise3 = optimizer.optimizeContent(request);

      const results = await Promise.all([promise1, promise2, promise3]);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.optimizedTitle).toBeDefined();
      });

      optimizer.destroy();
    });
  });

  describe('UI Component Integration', () => {
    const mockOnSuggestionApply = vi.fn();

    beforeEach(() => {
      mockOnSuggestionApply.mockClear();
    });

    it('should render enhanced SEO panel and perform analysis', async () => {
      const user = userEvent.setup();

      render(
        <EnhancedBlogFormSEOPanel
          content="Nepal is a beautiful country for trekking adventures."
          title="Nepal Trekking Guide"
          excerpt="A comprehensive guide to trekking in Nepal"
          tags={['nepal', 'trekking']}
          onSuggestionApply={mockOnSuggestionApply}
          enableRealTimeAnalysis={true}
        />
      );

      // Wait for real-time analysis to complete
      await waitFor(() => {
        expect(screen.getByText('Enhanced SEO Analysis')).toBeInTheDocument();
      }, { timeout: 3000 });

      // Check if optimization results are displayed
      await waitFor(() => {
        expect(screen.getByText(/Optimization Confidence/)).toBeInTheDocument();
      });

      // Verify optimized content is shown
      expect(screen.getByText('Ultimate Guide to Nepal Trekking Adventures')).toBeInTheDocument();
      expect(screen.getByText(/Discover the best trekking routes/)).toBeInTheDocument();

      // Test applying suggestions
      const applyTitleButton = screen.getByText('Apply Title');
      await user.click(applyTitleButton);

      expect(mockOnSuggestionApply).toHaveBeenCalledWith(
        'title',
        'Ultimate Guide to Nepal Trekking Adventures'
      );
    });

    it('should show performance metrics in monitoring tabs', async () => {
      const user = userEvent.setup();

      render(
        <EnhancedBlogFormSEOPanel
          content="Test content for performance monitoring"
          title="Test Title"
          excerpt="Test excerpt"
          tags={['test']}
          onSuggestionApply={mockOnSuggestionApply}
        />
      );

      // Wait for analysis to complete
      await waitFor(() => {
        expect(screen.getByText('Enhanced SEO Analysis')).toBeInTheDocument();
      });

      // Switch to performance tab
      const performanceTab = screen.getByText('Performance');
      await user.click(performanceTab);

      // Check performance metrics
      expect(screen.getByText(/Total Requests/)).toBeInTheDocument();
      expect(screen.getByText(/Success Rate/)).toBeInTheDocument();
      expect(screen.getByText(/Avg Processing Time/)).toBeInTheDocument();
    });

    it('should display health status and system information', async () => {
      const user = userEvent.setup();

      render(
        <EnhancedBlogFormSEOPanel
          content="Test content for health monitoring"
          title="Test Title"
          excerpt="Test excerpt"
          tags={['test']}
          onSuggestionApply={mockOnSuggestionApply}
        />
      );

      // Wait for analysis to complete
      await waitFor(() => {
        expect(screen.getByText('Enhanced SEO Analysis')).toBeInTheDocument();
      });

      // Switch to health tab
      const healthTab = screen.getByText('Health');
      await user.click(healthTab);

      // Check health information
      expect(screen.getByText('System Health')).toBeInTheDocument();
      expect(screen.getByText('Circuit Breaker')).toBeInTheDocument();
      expect(screen.getByText('Rate Limiting')).toBeInTheDocument();
      expect(screen.getByText('Retry Handler')).toBeInTheDocument();
    });

    it('should handle errors gracefully in UI', async () => {
      // Mock API error
      (global.fetch as any).mockRejectedValue(new Error('API Error'));

      render(
        <EnhancedBlogFormSEOPanel
          content="Test content that will cause error"
          title="Test Title"
          excerpt="Test excerpt"
          tags={['test']}
          onSuggestionApply={mockOnSuggestionApply}
        />
      );

      // Wait for error to be displayed
      await waitFor(() => {
        expect(screen.getByText(/SEO optimization failed/)).toBeInTheDocument();
      }, { timeout: 3000 });
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent optimizations', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key',
        rateLimit: {
          maxTokens: 5,
          refillRate: 2,
          refillInterval: 100,
          queueSize: 20,
          requestTimeout: 5000
        }
      });

      const requests = Array.from({ length: 10 }, (_, i) => ({
        content: `Test content ${i} about Nepal trekking adventures.`,
        title: `Test Title ${i}`,
        contentType: 'blog' as const
      }));

      const startTime = Date.now();
      const results = await Promise.all(
        requests.map(request => optimizer.optimizeContent(request))
      );
      const endTime = Date.now();

      expect(results).toHaveLength(10);
      results.forEach((result, i) => {
        expect(result.optimizedTitle).toBeDefined();
        expect(result.processingTime).toBeGreaterThan(0);
      });

      // Verify all requests completed within reasonable time
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds max

      // Verify statistics
      const stats = optimizer.getStats();
      expect(stats.totalRequests).toBe(10);
      expect(stats.successfulOptimizations).toBe(10);

      optimizer.destroy();
    });

    it('should maintain performance under load', async () => {
      const optimizer = new EnhancedSEOOptimizer({
        geminiApiKey: 'test-api-key'
      });

      const batchSize = 5;
      const batches = 3;
      const allResults: any[] = [];

      for (let batch = 0; batch < batches; batch++) {
        const requests = Array.from({ length: batchSize }, (_, i) => ({
          content: `Batch ${batch} content ${i} about Nepal trekking.`,
          title: `Batch ${batch} Title ${i}`,
          contentType: 'blog' as const
        }));

        const batchResults = await Promise.all(
          requests.map(request => optimizer.optimizeContent(request))
        );

        allResults.push(...batchResults);

        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      expect(allResults).toHaveLength(batchSize * batches);

      // Verify performance metrics
      const stats = optimizer.getStats();
      expect(stats.averageProcessingTime).toBeLessThan(5000); // 5 seconds max average
      expect(stats.successfulOptimizations).toBe(batchSize * batches);

      optimizer.destroy();
    });
  });
});
