import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, vi, expect, beforeEach } from 'vitest';
import BlogFormModal from '../components/admin/BlogFormModal';
import { mockSupabase } from './test-utils';
import { BlogPost } from '../types/database';
import '@testing-library/jest-dom';

vi.mock('../integrations/supabase/client', () => ({
  supabase: mockSupabase
}));

vi.mock('../components/admin/RichTextEditor', () => ({
  default: ({ value, onChange, placeholder }: { value: string; onChange: (value: string) => void; placeholder?: string }) => (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      data-testid="rich-text-editor"
    />
  )
}));

describe('BlogFormModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly in create mode', () => {
    render(<BlogFormModal {...defaultProps} />);
    expect(screen.getByText('Add New Blog Post')).toBeInTheDocument();
    expect(screen.getByLabelText('Title')).toBeInTheDocument();
    expect(screen.getByLabelText('Slug')).toBeInTheDocument();
  });

  it('automatically generates slug from title', async () => {
    render(<BlogFormModal {...defaultProps} />);
    const titleInput = screen.getByLabelText('Title');
    await userEvent.type(titleInput, 'Test Blog Post');
    expect(screen.getByLabelText('Slug')).toHaveValue('test-blog-post');
  });

  it('handles form submission', async () => {
    const onClose = vi.fn();
    render(<BlogFormModal {...defaultProps} onClose={onClose} />);

    await userEvent.type(screen.getByLabelText('Title'), 'Test Post');
    await userEvent.type(screen.getByLabelText('Excerpt'), 'Test excerpt');
    await userEvent.type(screen.getByTestId('rich-text-editor'), 'Test content');

    fireEvent.click(screen.getByRole('button', { name: 'Create' }));

    await waitFor(() => {
      expect(mockSupabase.from).toHaveBeenCalledWith('blog_posts');
      expect(onClose).toHaveBeenCalled();
    });
  });

  it('autosaves draft while editing', async () => {
    const blog: BlogPost = {
      id: 'test-id',
      title: 'Test Blog',
      content: 'Initial content',
      excerpt: 'Test excerpt',
      slug: 'test-blog',
      published: true,
      tags: ['test'],
      cover_image: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      author_id: 'test-author',
    };

    render(<BlogFormModal {...defaultProps} blog={blog} />);

    const editor = screen.getByTestId('rich-text-editor');
    await userEvent.type(editor, ' updated');

    await waitFor(
      () => {
        expect(mockSupabase.from).toHaveBeenCalledWith('blog_drafts');
      },
      { timeout: 2500 }
    );
  });

  it('loads SEO suggestions', async () => {
    const mockResponse = {
      suggestions: [
        {
          type: 'title',
          suggestion: 'Test Blog - Complete Guide',
          reason: 'Title could be more descriptive for better SEO',
          impact: 'medium'
        }
      ],
      score: 65,
      metrics: {
        readability: 70,
        keywordDensity: 1.2,
        wordCount: 45,
        titleLength: 9,
        metaLength: 0
      }
    };

    mockSupabase.functions.invoke.mockResolvedValue({
      data: mockResponse,
      error: null
    });

    render(<BlogFormModal {...defaultProps} />);

    await userEvent.type(screen.getByLabelText('Title'), 'Test Blog');
    await userEvent.type(screen.getByTestId('rich-text-editor'), 'Test content for SEO analysis');

    await waitFor(() => {
      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('seo-optimizer', {
        body: expect.objectContaining({
          content: expect.stringContaining('Test content'),
          title: 'Test Blog',
          excerpt: '',
          tags: []
        }),
      });
    }, { timeout: 2000 });
  });

  it('handles error states gracefully', async () => {
    const mockError = new Error('Test error');
    mockSupabase.from.mockImplementationOnce(() => ({
      insert: vi.fn().mockResolvedValue({ error: mockError }),
      update: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      select: vi.fn(),
      eq: vi.fn(),
    }));

    render(<BlogFormModal {...defaultProps} />);

    await userEvent.type(screen.getByLabelText('Title'), 'Test Post');
    fireEvent.click(screen.getByRole('button', { name: 'Create' }));

    await waitFor(() => {
      expect(screen.getByText('Failed to save blog post')).toBeInTheDocument();
    });
  });
});
