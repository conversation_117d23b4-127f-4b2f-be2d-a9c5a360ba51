/**
 * Test Suite for Content Versioning System
 * Tests for version management, comparison, and rollback functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ContentVersioningManager, ContentVersion, OptimizationMetadata } from '@/lib/seo/content-versioning';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('ContentVersioningManager', () => {
  let versioningManager: ContentVersioningManager;
  const testContentId = 'test-content-123';
  const testUserId = 'user-456';

  const mockOptimizationData: OptimizationMetadata = {
    optimizationType: 'ai-enhanced',
    confidence: 8,
    estimatedImpact: 'high',
    focusKeywords: ['nepal', 'trekking'],
    technicalRecommendations: ['Add more internal links'],
    processingTime: 1500,
    aiModel: 'gemini-pro',
    optimizerVersion: '2.0.0'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    versioningManager = new ContentVersioningManager();
  });

  describe('Version Creation', () => {
    it('should create a new version successfully', async () => {
      const versionData = {
        title: 'Test Blog Post',
        content: 'This is test content about Nepal trekking.',
        metaDescription: 'A test meta description.',
        tags: ['nepal', 'trekking', 'adventure'],
        seoScore: 7.5,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      const version = await versioningManager.createVersion(
        testContentId,
        'blog',
        versionData
      );

      expect(version.id).toBeDefined();
      expect(version.contentId).toBe(testContentId);
      expect(version.contentType).toBe('blog');
      expect(version.version).toBe(1);
      expect(version.title).toBe(versionData.title);
      expect(version.content).toBe(versionData.content);
      expect(version.metaDescription).toBe(versionData.metaDescription);
      expect(version.tags).toEqual(versionData.tags);
      expect(version.seoScore).toBe(versionData.seoScore);
      expect(version.isActive).toBe(true);
      expect(version.createdBy).toBe(testUserId);
      expect(version.createdAt).toBeInstanceOf(Date);
    });

    it('should increment version number for subsequent versions', async () => {
      const versionData = {
        title: 'Test Blog Post',
        content: 'This is test content.',
        metaDescription: 'A test meta description.',
        tags: ['test'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      // Create first version
      const version1 = await versioningManager.createVersion(
        testContentId,
        'blog',
        versionData
      );

      // Create second version
      const version2 = await versioningManager.createVersion(
        testContentId,
        'blog',
        { ...versionData, title: 'Updated Title' }
      );

      expect(version1.version).toBe(1);
      expect(version2.version).toBe(2);
      expect(version1.isActive).toBe(false);
      expect(version2.isActive).toBe(true);
    });

    it('should save versions to localStorage', async () => {
      const versionData = {
        title: 'Test Blog Post',
        content: 'This is test content.',
        metaDescription: 'A test meta description.',
        tags: ['test'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      await versioningManager.createVersion(testContentId, 'blog', versionData);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'seo_content_versions',
        expect.any(String)
      );
    });
  });

  describe('Version Retrieval', () => {
    beforeEach(async () => {
      // Create test versions
      const versionData = {
        title: 'Test Blog Post',
        content: 'This is test content.',
        metaDescription: 'A test meta description.',
        tags: ['test'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      await versioningManager.createVersion(testContentId, 'blog', versionData);
      await versioningManager.createVersion(testContentId, 'blog', {
        ...versionData,
        title: 'Updated Title',
        seoScore: 8.0
      });
    });

    it('should retrieve all versions for content', () => {
      const versions = versioningManager.getVersions(testContentId);
      expect(versions).toHaveLength(2);
      expect(versions[0].version).toBe(1);
      expect(versions[1].version).toBe(2);
    });

    it('should retrieve active version', () => {
      const activeVersion = versioningManager.getActiveVersion(testContentId);
      expect(activeVersion).toBeDefined();
      expect(activeVersion!.version).toBe(2);
      expect(activeVersion!.isActive).toBe(true);
      expect(activeVersion!.title).toBe('Updated Title');
    });

    it('should retrieve version by ID', () => {
      const versions = versioningManager.getVersions(testContentId);
      const firstVersion = versions[0];

      const retrievedVersion = versioningManager.getVersion(firstVersion.id);
      expect(retrievedVersion).toEqual(firstVersion);
    });

    it('should return null for non-existent version', () => {
      const version = versioningManager.getVersion('non-existent-id');
      expect(version).toBeNull();
    });
  });

  describe('Version Rollback', () => {
    let version1: ContentVersion;
    let version2: ContentVersion;

    beforeEach(async () => {
      const versionData = {
        title: 'Original Title',
        content: 'Original content.',
        metaDescription: 'Original meta description.',
        tags: ['original'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      version1 = await versioningManager.createVersion(testContentId, 'blog', versionData);
      version2 = await versioningManager.createVersion(testContentId, 'blog', {
        ...versionData,
        title: 'Updated Title',
        seoScore: 8.0
      });
    });

    it('should rollback to previous version', async () => {
      const rollbackVersion = await versioningManager.rollbackToVersion(
        version1.id,
        'admin-user'
      );

      expect(rollbackVersion.title).toBe('Original Title');
      expect(rollbackVersion.content).toBe('Original content.');
      expect(rollbackVersion.version).toBe(3); // New version number
      expect(rollbackVersion.isActive).toBe(true);
      expect(rollbackVersion.parentVersionId).toBe(version1.id);
      expect(rollbackVersion.createdBy).toBe('admin-user');

      // Previous active version should be deactivated
      const versions = versioningManager.getVersions(testContentId);
      const previousActive = versions.find(v => v.id === version2.id);
      expect(previousActive!.isActive).toBe(false);
    });

    it('should throw error for non-existent version rollback', async () => {
      await expect(
        versioningManager.rollbackToVersion('non-existent-id', 'admin-user')
      ).rejects.toThrow('Version not found');
    });
  });

  describe('Version Comparison', () => {
    let version1: ContentVersion;
    let version2: ContentVersion;

    beforeEach(async () => {
      const versionData1 = {
        title: 'Original Title',
        content: 'Original content about Nepal trekking.',
        metaDescription: 'Original meta description.',
        tags: ['nepal', 'trekking'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      const versionData2 = {
        title: 'Optimized Title for Better SEO',
        content: 'Enhanced content about Nepal trekking adventures with more details.',
        metaDescription: 'Optimized meta description for better search visibility.',
        tags: ['nepal', 'trekking', 'adventure', 'hiking'],
        seoScore: 8.5,
        optimizationData: {
          ...mockOptimizationData,
          confidence: 9,
          estimatedImpact: 'high' as const,
          focusKeywords: ['nepal', 'trekking', 'adventure', 'hiking']
        },
        createdBy: testUserId
      };

      version1 = await versioningManager.createVersion(testContentId, 'blog', versionData1);
      version2 = await versioningManager.createVersion(testContentId, 'blog', versionData2);
    });

    it('should compare two versions successfully', () => {
      const comparison = versioningManager.compareVersions(version1.id, version2.id);

      expect(comparison).toBeDefined();
      expect(comparison!.oldVersion.id).toBe(version1.id);
      expect(comparison!.newVersion.id).toBe(version2.id);
      expect(comparison!.changes).toBeDefined();
      expect(comparison!.impactAnalysis).toBeDefined();
    });

    it('should detect title changes', () => {
      const comparison = versioningManager.compareVersions(version1.id, version2.id);
      const titleChange = comparison!.changes.find(c => c.field === 'title');

      expect(titleChange).toBeDefined();
      expect(titleChange!.type).toBe('modified');
      expect(titleChange!.oldValue).toBe('Original Title');
      expect(titleChange!.newValue).toBe('Optimized Title for Better SEO');
      expect(titleChange!.impact).toBe('high');
    });

    it('should detect tag additions', () => {
      const comparison = versioningManager.compareVersions(version1.id, version2.id);
      const tagAddition = comparison!.changes.find(c => c.field === 'tags' && c.type === 'added');

      expect(tagAddition).toBeDefined();
      expect(tagAddition!.newValue).toEqual(['adventure', 'hiking']);
    });

    it('should calculate impact analysis', () => {
      const comparison = versioningManager.compareVersions(version1.id, version2.id);
      const impact = comparison!.impactAnalysis;

      expect(impact.seoScoreChange).toBe(1.5);
      expect(impact.confidenceChange).toBe(1);
      expect(impact.estimatedTrafficImpact).toBe(3.0); // 1.5 * 2
      expect(impact.keywordImprovements).toContain('adventure');
      expect(impact.keywordImprovements).toContain('hiking');
    });

    it('should return null for invalid version comparison', () => {
      const comparison = versioningManager.compareVersions('invalid-id', version2.id);
      expect(comparison).toBeNull();
    });
  });

  describe('Optimization History', () => {
    beforeEach(async () => {
      const baseData = {
        title: 'Test Post',
        content: 'Test content.',
        metaDescription: 'Test meta.',
        tags: ['test'],
        seoScore: 7.0,
        createdBy: testUserId
      };

      // Create manual version
      await versioningManager.createVersion(testContentId, 'blog', {
        ...baseData,
        optimizationData: { ...mockOptimizationData, optimizationType: 'manual' }
      });

      // Create AI-enhanced version
      await versioningManager.createVersion(testContentId, 'blog', {
        ...baseData,
        optimizationData: { ...mockOptimizationData, optimizationType: 'ai-enhanced' }
      });

      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      // Create automated version
      await versioningManager.createVersion(testContentId, 'blog', {
        ...baseData,
        optimizationData: { ...mockOptimizationData, optimizationType: 'automated' }
      });
    });

    it('should return optimization history excluding manual versions', () => {
      const history = versioningManager.getOptimizationHistory(testContentId);

      expect(history).toHaveLength(2);
      expect(history[0].optimizationData.optimizationType).toBe('automated');
      expect(history[1].optimizationData.optimizationType).toBe('ai-enhanced');
    });

    it('should sort history by creation date descending', () => {
      const history = versioningManager.getOptimizationHistory(testContentId);

      for (let i = 1; i < history.length; i++) {
        expect(history[i - 1].createdAt.getTime()).toBeGreaterThanOrEqual(
          history[i].createdAt.getTime()
        );
      }
    });
  });

  describe('Statistics', () => {
    beforeEach(async () => {
      const baseData = {
        title: 'Test Post',
        content: 'Test content.',
        metaDescription: 'Test meta.',
        tags: ['test'],
        createdBy: testUserId
      };

      // Create versions with different scores
      await versioningManager.createVersion('content-1', 'blog', {
        ...baseData,
        seoScore: 6.0,
        optimizationData: mockOptimizationData
      });

      const parentVersion = await versioningManager.createVersion('content-1', 'blog', {
        ...baseData,
        seoScore: 8.0,
        optimizationData: mockOptimizationData,
        parentVersionId: undefined
      });

      await versioningManager.createVersion('content-2', 'blog', {
        ...baseData,
        seoScore: 7.5,
        optimizationData: mockOptimizationData
      });
    });

    it('should calculate statistics correctly', () => {
      const stats = versioningManager.getStats();

      expect(stats.totalVersions).toBe(3);
      expect(stats.activeVersions).toBe(2);
      expect(stats.mostImprovedContent).toBeDefined();
      expect(stats.recentOptimizations).toBeDefined();
    });
  });

  describe('Cleanup', () => {
    beforeEach(async () => {
      const baseData = {
        title: 'Test Post',
        content: 'Test content.',
        metaDescription: 'Test meta.',
        tags: ['test'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      // Create multiple versions
      for (let i = 0; i < 15; i++) {
        await versioningManager.createVersion(testContentId, 'blog', {
          ...baseData,
          title: `Test Post ${i + 1}`
        });
      }
    });

    it('should cleanup old versions keeping specified number', async () => {
      const deletedCount = await versioningManager.cleanupOldVersions(10);

      expect(deletedCount).toBe(5);

      const remainingVersions = versioningManager.getVersions(testContentId);
      expect(remainingVersions).toHaveLength(10);
    });
  });

  describe('Import/Export', () => {
    let testVersions: ContentVersion[];

    beforeEach(async () => {
      const baseData = {
        title: 'Test Post',
        content: 'Test content.',
        metaDescription: 'Test meta.',
        tags: ['test'],
        seoScore: 7.0,
        optimizationData: mockOptimizationData,
        createdBy: testUserId
      };

      await versioningManager.createVersion(testContentId, 'blog', baseData);
      await versioningManager.createVersion(testContentId, 'blog', {
        ...baseData,
        title: 'Updated Title'
      });

      testVersions = versioningManager.getVersions(testContentId);
    });

    it('should export version history as JSON', () => {
      const exported = versioningManager.exportVersionHistory(testContentId);
      const parsed = JSON.parse(exported);

      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0].title).toBe('Test Post');
      expect(parsed[1].title).toBe('Updated Title');
    });

    it('should import version history from JSON', async () => {
      const exported = versioningManager.exportVersionHistory(testContentId);
      const newContentId = 'imported-content-456';

      const importedCount = await versioningManager.importVersionHistory(
        newContentId,
        exported
      );

      expect(importedCount).toBe(2);

      const importedVersions = versioningManager.getVersions(newContentId);
      expect(importedVersions).toHaveLength(2);
      expect(importedVersions[0].title).toBe('Test Post');
      expect(importedVersions[1].title).toBe('Updated Title');
    });

    it('should handle invalid JSON during import', async () => {
      await expect(
        versioningManager.importVersionHistory(testContentId, 'invalid json')
      ).rejects.toThrow('Failed to import version history');
    });
  });
});
