/**
 * Test Suite for Enhanced SEO Optimizer
 * Comprehensive tests for circuit breaker, retry logic, rate limiting, and optimization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedSEOOptimizer, SEOOptimizationRequest } from '@/lib/seo/enhanced-optimizer';
import { CircuitBreaker, CircuitState } from '@/lib/seo/circuit-breaker';
import { RetryHandler } from '@/lib/seo/retry-handler';
import { RateLimiter } from '@/lib/seo/rate-limiter';

// Mock fetch globally
global.fetch = vi.fn();

describe('EnhancedSEOOptimizer', () => {
  let optimizer: EnhancedSEOOptimizer;
  const mockApiKey = 'test-api-key';

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock environment variable
    vi.stubEnv('VITE_GEMINI_API_KEY', mockApiKey);

    optimizer = new EnhancedSEOOptimizer({
      geminiApiKey: mockApiKey,
      circuitBreaker: {
        failureThreshold: 3,
        resetTimeout: 1000,
        monitoringPeriod: 5000,
        expectedErrors: ['rate limit']
      },
      retry: {
        maxRetries: 2,
        initialDelay: 100,
        maxDelay: 1000,
        backoffMultiplier: 2,
        retryableErrors: ['timeout', 'server error'],
        retryableStatusCodes: [429, 500, 502, 503, 504]
      },
      rateLimit: {
        maxTokens: 5,
        refillRate: 1,
        refillInterval: 100,
        queueSize: 10,
        requestTimeout: 5000
      }
    });
  });

  afterEach(() => {
    optimizer.destroy();
    vi.unstubAllEnvs();
  });

  describe('Successful Optimization', () => {
    it('should successfully optimize content with valid response', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                optimizedTitle: 'Optimized Title for SEO',
                optimizedMetaDescription: 'This is an optimized meta description for better search engine visibility.',
                focusKeywords: ['nepal', 'trekking', 'adventure'],
                contentSuggestions: [],
                technicalRecommendations: ['Add more internal links'],
                optimizationConfidence: 8,
                estimatedImpact: 'high'
              })
            }]
          }
        }]
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request: SEOOptimizationRequest = {
        content: 'Test content about Nepal trekking adventures.',
        title: 'Nepal Trekking Guide',
        excerpt: 'A comprehensive guide to trekking in Nepal.',
        tags: ['nepal', 'trekking'],
        contentType: 'blog'
      };

      const result = await optimizer.optimizeContent(request);

      expect(result.optimizedTitle).toBe('Optimized Title for SEO');
      expect(result.optimizedMetaDescription).toBe('This is an optimized meta description for better search engine visibility.');
      expect(result.focusKeywords).toEqual(['nepal', 'trekking', 'adventure']);
      expect(result.optimizationConfidence).toBe(8);
      expect(result.estimatedImpact).toBe('high');
      expect(result.processingTime).toBeGreaterThan(0);
      expect(result.version).toBe('2.0.0');
    });

    it('should track optimization statistics', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                optimizedTitle: 'Test Title',
                optimizedMetaDescription: 'Test description',
                focusKeywords: [],
                contentSuggestions: [],
                technicalRecommendations: [],
                optimizationConfidence: 5,
                estimatedImpact: 'medium'
              })
            }]
          }
        }]
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request: SEOOptimizationRequest = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog'
      };

      await optimizer.optimizeContent(request);

      const stats = optimizer.getStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.successfulOptimizations).toBe(1);
      expect(stats.failedOptimizations).toBe(0);
      expect(stats.averageProcessingTime).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      (global.fetch as any).mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve('Internal Server Error')
      });

      const request: SEOOptimizationRequest = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog'
      };

      await expect(optimizer.optimizeContent(request)).rejects.toThrow();

      const stats = optimizer.getStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.failedOptimizations).toBe(1);
    });

    it('should validate input parameters', async () => {
      const invalidRequest: SEOOptimizationRequest = {
        content: '',
        title: 'Test Title',
        contentType: 'blog'
      };

      await expect(optimizer.optimizeContent(invalidRequest)).rejects.toThrow('Content is required for optimization');
    });

    it('should handle malformed JSON responses', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: 'Invalid JSON response'
            }]
          }
        }]
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request: SEOOptimizationRequest = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog'
      };

      await expect(optimizer.optimizeContent(request)).rejects.toThrow('No JSON found in response');
    });
  });

  describe('Fallback Mechanisms', () => {
    it('should use fallback optimization when API fails', async () => {
      const optimizerWithFallback = new EnhancedSEOOptimizer({
        geminiApiKey: mockApiKey,
        enableFallback: true
      });

      (global.fetch as any).mockResolvedValue({
        ok: false,
        status: 429,
        text: () => Promise.resolve('Rate limit exceeded')
      });

      const request: SEOOptimizationRequest = {
        content: 'Test content about Nepal trekking adventures.',
        title: 'Very Long Title That Exceeds The Recommended Length For SEO Optimization',
        excerpt: 'Test excerpt',
        tags: ['nepal', 'trekking'],
        contentType: 'blog'
      };

      const result = await optimizerWithFallback.optimizeContent(request);

      expect(result.optimizedTitle).toContain('...');
      expect(result.optimizationConfidence).toBe(3);
      expect(result.estimatedImpact).toBe('low');
      expect(result.version).toBe('2.0.0-fallback');
      expect(result.technicalRecommendations).toContain('AI optimization temporarily unavailable - using basic optimization');

      optimizerWithFallback.destroy();
    });
  });

  describe('Health Check', () => {
    it('should report healthy status initially', async () => {
      const health = await optimizer.healthCheck();
      expect(health.status).toBe('healthy');
    });

    it('should report unhealthy status when circuit breaker is open', async () => {
      // Force circuit breaker to open by causing failures
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const request: SEOOptimizationRequest = {
        content: 'Test content',
        title: 'Test Title',
        contentType: 'blog'
      };

      // Cause enough failures to open circuit breaker
      for (let i = 0; i < 4; i++) {
        try {
          await optimizer.optimizeContent(request);
        } catch (error) {
          // Expected to fail
        }
      }

      const health = await optimizer.healthCheck();
      expect(health.status).toBe('unhealthy');
      expect(health.details.reason).toBe('Circuit breaker is open');
    });
  });
});

describe('CircuitBreaker', () => {
  let circuitBreaker: CircuitBreaker;

  beforeEach(() => {
    circuitBreaker = new CircuitBreaker({
      failureThreshold: 3,
      resetTimeout: 1000,
      monitoringPeriod: 5000,
      expectedErrors: ['expected error']
    });
  });

  it('should start in CLOSED state', () => {
    const stats = circuitBreaker.getStats();
    expect(stats.state).toBe(CircuitState.CLOSED);
    expect(stats.failureCount).toBe(0);
  });

  it('should open after threshold failures', async () => {
    const failingOperation = () => Promise.reject(new Error('Test error'));

    // Cause failures to reach threshold
    for (let i = 0; i < 3; i++) {
      try {
        await circuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected to fail
      }
    }

    const stats = circuitBreaker.getStats();
    expect(stats.state).toBe(CircuitState.OPEN);
    expect(stats.failureCount).toBe(3);
  });

  it('should not count expected errors towards failure threshold', async () => {
    const expectedErrorOperation = () => Promise.reject(new Error('expected error'));

    // Cause expected errors
    for (let i = 0; i < 5; i++) {
      try {
        await circuitBreaker.execute(expectedErrorOperation);
      } catch (error) {
        // Expected to fail
      }
    }

    const stats = circuitBreaker.getStats();
    expect(stats.state).toBe(CircuitState.CLOSED); // Should remain closed
  });

  it('should reset after successful operation in HALF_OPEN state', async () => {
    const failingOperation = () => Promise.reject(new Error('Test error'));
    const successfulOperation = () => Promise.resolve('success');

    // Open the circuit breaker
    for (let i = 0; i < 3; i++) {
      try {
        await circuitBreaker.execute(failingOperation);
      } catch (error) {
        // Expected to fail
      }
    }

    expect(circuitBreaker.getStats().state).toBe(CircuitState.OPEN);

    // Wait for reset timeout
    await new Promise(resolve => setTimeout(resolve, 1100));

    // Execute successful operation
    const result = await circuitBreaker.execute(successfulOperation);
    expect(result).toBe('success');
    expect(circuitBreaker.getStats().state).toBe(CircuitState.CLOSED);
  });
});

describe('RetryHandler', () => {
  let retryHandler: RetryHandler;

  beforeEach(() => {
    retryHandler = new RetryHandler({
      maxRetries: 2,
      initialDelay: 10,
      maxDelay: 100,
      backoffMultiplier: 2,
      retryableErrors: ['timeout', 'network error'],
      retryableStatusCodes: [429, 500]
    });
  });

  it('should succeed on first attempt', async () => {
    const successfulOperation = vi.fn(() => Promise.resolve('success'));

    const result = await retryHandler.execute(successfulOperation);

    expect(result).toBe('success');
    expect(successfulOperation).toHaveBeenCalledTimes(1);
  });

  it('should retry on retryable errors', async () => {
    const operation = vi.fn()
      .mockRejectedValueOnce(new Error('timeout'))
      .mockRejectedValueOnce(new Error('network error'))
      .mockResolvedValueOnce('success');

    const result = await retryHandler.execute(operation);

    expect(result).toBe('success');
    expect(operation).toHaveBeenCalledTimes(3);
  });

  it('should not retry on non-retryable errors', async () => {
    const operation = vi.fn().mockRejectedValue(new Error('validation error'));

    await expect(retryHandler.execute(operation)).rejects.toThrow('validation error');
    expect(operation).toHaveBeenCalledTimes(1);
  });

  it('should fail after max retries', async () => {
    const operation = vi.fn().mockRejectedValue(new Error('timeout'));

    await expect(retryHandler.execute(operation)).rejects.toThrow('Operation failed after 3 attempts');
    expect(operation).toHaveBeenCalledTimes(3);
  });
});

describe('RateLimiter', () => {
  let rateLimiter: RateLimiter;

  beforeEach(() => {
    rateLimiter = new RateLimiter({
      maxTokens: 2,
      refillRate: 1,
      refillInterval: 100,
      queueSize: 5,
      requestTimeout: 1000
    });
  });

  afterEach(() => {
    rateLimiter.destroy();
  });

  it('should execute immediately when tokens are available', async () => {
    const operation = vi.fn(() => Promise.resolve('success'));

    const result = await rateLimiter.execute(operation);

    expect(result).toBe('success');
    expect(operation).toHaveBeenCalledTimes(1);
  });

  it('should queue requests when no tokens available', async () => {
    const operation = vi.fn(() => Promise.resolve('success'));

    // Use up all tokens
    const promises = [
      rateLimiter.execute(operation),
      rateLimiter.execute(operation),
      rateLimiter.execute(operation) // This should be queued
    ];

    const results = await Promise.all(promises);

    expect(results).toEqual(['success', 'success', 'success']);
    expect(operation).toHaveBeenCalledTimes(3);
  });

  it('should reject requests when queue is full', async () => {
    const operation = vi.fn(() => Promise.resolve('success'));

    // Fill up tokens and queue
    const promises = [];
    for (let i = 0; i < 8; i++) { // 2 tokens + 5 queue + 1 overflow
      promises.push(rateLimiter.execute(operation));
    }

    await expect(Promise.all(promises)).rejects.toThrow('Rate limit queue is full');
  });

  it('should refill tokens over time', async () => {
    const operation = vi.fn(() => Promise.resolve('success'));

    // Use up all tokens
    await rateLimiter.execute(operation);
    await rateLimiter.execute(operation);

    expect(rateLimiter.getAvailableTokens()).toBe(0);

    // Wait for token refill
    await new Promise(resolve => setTimeout(resolve, 150));

    expect(rateLimiter.getAvailableTokens()).toBeGreaterThan(0);
  });
});
