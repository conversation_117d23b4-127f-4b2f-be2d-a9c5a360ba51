import '@testing-library/jest-dom';
import { vi, expect } from 'vitest';
import { mockSupabase } from './test-utils';
import type { Mock } from 'vitest';

// Mock matchMedia which is required by some UI components
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
(global as any).ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock Supabase client
vi.mock('../integrations/supabase/client', () => ({
  supabase: mockSupabase,
  callFunction: vi.fn()
}));

// Mock localStorage for content versioning
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock environment variables
vi.stubEnv('VITE_GEMINI_API_KEY', 'test-gemini-api-key');
vi.stubEnv('VITE_SUPABASE_URL', 'https://test.supabase.co');
vi.stubEnv('VITE_SUPABASE_ANON_KEY', 'test-anon-key');

type MatcherFunction = (received: Mock, ...expected: unknown[]) => {
  pass: boolean;
  message: () => string;
};

// Extend expect matchers
expect.extend({
  toHaveBeenCalledWithMatch: ((received: Mock, ...expected: unknown[]) => {
    const pass = expected.every((arg, i) => {
      if (typeof arg === 'object' && arg !== null) {
        return JSON.stringify(received.mock.calls[0][i]).includes(JSON.stringify(arg));
      }
      return received.mock.calls[0][i] === arg;
    });

    return {
      message: () =>
        `expected ${received} ${pass ? 'not ' : ''}to have been called with arguments matching ${expected}`,
      pass,
    };
  }) as MatcherFunction,
});

// Add custom matchers to TypeScript
declare module 'vitest' {
  interface Assertion {
    /**
     * Check if a mock was called with arguments that partially match the expected arguments
     * @param args - The expected arguments
     */
    toHaveBeenCalledWithMatch(...args: unknown[]): boolean;
  }
}

export {};
