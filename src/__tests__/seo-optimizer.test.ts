import { describe, it, expect, beforeEach, vi } from 'vitest';
import { supabase } from '@/integrations/supabase/client';

// Mock the supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    functions: {
      invoke: vi.fn()
    }
  }
}));

describe('SEO Optimizer Function', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Real-time Analysis', () => {
    it('should return SEO analysis for valid content', async () => {
      const mockResponse = {
        suggestions: [
          {
            type: 'title',
            suggestion: 'Everest Base Camp Trek - Complete Guide',
            reason: 'Title could be more descriptive',
            impact: 'medium'
          }
        ],
        score: 75,
        metrics: {
          readability: 65,
          keywordDensity: 2.1,
          wordCount: 150,
          titleLength: 25,
          metaLength: 140
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data, error } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'This is a test blog post about trekking in Nepal. It contains information about the Everest Base Camp trek and other adventures.',
          title: 'Everest Base Camp Trek',
          excerpt: 'Learn about the amazing Everest Base Camp trek experience in Nepal with detailed information and tips.',
          tags: ['everest', 'trekking', 'nepal']
        }
      });

      expect(error).toBeNull();
      expect(data).toEqual(mockResponse);
      expect(data.suggestions).toHaveLength(1);
      expect(data.score).toBe(75);
      expect(data.metrics).toBeDefined();
    });

    it('should handle empty content gracefully', async () => {
      const mockResponse = {
        suggestions: [
          {
            type: 'content',
            suggestion: 'Content appears to be quite short. Consider expanding with more detailed information.',
            reason: 'Content has 0 words. Aim for at least 300 words for better SEO performance.',
            impact: 'high'
          }
        ],
        score: 0,
        metrics: {
          readability: 0,
          keywordDensity: 0,
          wordCount: 0,
          titleLength: 10,
          metaLength: 0
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data, error } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: '',
          title: 'Test Title',
          excerpt: '',
          tags: []
        }
      });

      expect(error).toBeNull();
      expect(data.score).toBe(0);
      expect(data.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'content',
          impact: 'high'
        })
      );
    });

    it('should provide title suggestions for long titles', async () => {
      const longTitle = 'This is a very long title that exceeds the recommended 60 character limit for SEO optimization';
      
      const mockResponse = {
        suggestions: [
          {
            type: 'title',
            suggestion: 'This is a very long title that exceeds the recomm...',
            reason: `Title is ${longTitle.length} characters. Keep it under 60 for better search visibility.`,
            impact: 'high'
          }
        ],
        score: 45,
        metrics: {
          readability: 70,
          keywordDensity: 1.5,
          wordCount: 200,
          titleLength: longTitle.length,
          metaLength: 150
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'Sample content with enough words to test the SEO analysis functionality.',
          title: longTitle,
          excerpt: 'This is a sample excerpt for testing purposes.',
          tags: ['test', 'seo']
        }
      });

      expect(data.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'title',
          impact: 'high'
        })
      );
    });

    it('should suggest meta description improvements', async () => {
      const shortExcerpt = 'Too short';
      
      const mockResponse = {
        suggestions: [
          {
            type: 'meta',
            suggestion: 'Too short Learn more about this amazing adventure in Nepal.',
            reason: 'Meta description could be longer. Aim for 150-160 characters to maximize search snippet space.',
            impact: 'medium'
          }
        ],
        score: 60,
        metrics: {
          readability: 75,
          keywordDensity: 2.0,
          wordCount: 300,
          titleLength: 45,
          metaLength: shortExcerpt.length
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'This is a longer piece of content that should have enough words to pass the word count test for SEO analysis.',
          title: 'Good Title Length',
          excerpt: shortExcerpt,
          tags: ['test']
        }
      });

      expect(data.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'meta',
          impact: 'medium'
        })
      );
    });

    it('should handle keyword density analysis', async () => {
      const mockResponse = {
        suggestions: [
          {
            type: 'keyword',
            suggestion: 'Naturally incorporate these keywords: everest, trekking, nepal',
            reason: 'Keyword density is low. Include your target keywords more naturally throughout the content.',
            impact: 'medium'
          }
        ],
        score: 55,
        metrics: {
          readability: 70,
          keywordDensity: 0.3,
          wordCount: 250,
          titleLength: 50,
          metaLength: 155
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'This content does not contain the target keywords naturally integrated throughout the text.',
          title: 'Sample Blog Post About Adventure Travel',
          excerpt: 'This is a well-sized excerpt that should meet the character requirements for meta descriptions in search engines.',
          tags: ['everest', 'trekking', 'nepal']
        }
      });

      expect(data.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'keyword',
          impact: 'medium'
        })
      );
    });

    it('should handle function errors gracefully', async () => {
      (supabase.functions.invoke as any).mockResolvedValue({
        data: null,
        error: { message: 'Function execution failed' }
      });

      const { data, error } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'Test content',
          title: 'Test Title',
          excerpt: 'Test excerpt',
          tags: ['test']
        }
      });

      expect(error).toBeDefined();
      expect(error.message).toBe('Function execution failed');
    });
  });

  describe('Score Calculation', () => {
    it('should return high scores for well-optimized content', async () => {
      const mockResponse = {
        suggestions: [],
        score: 95,
        metrics: {
          readability: 85,
          keywordDensity: 2.5,
          wordCount: 500,
          titleLength: 55,
          metaLength: 158
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'This is well-optimized content with proper keyword density, good readability, and sufficient length for SEO purposes.',
          title: 'Perfect SEO Title Length With Keywords Included',
          excerpt: 'This meta description is perfectly sized for search engines and contains relevant keywords that will help with SEO rankings.',
          tags: ['seo', 'optimization', 'content']
        }
      });

      expect(data.score).toBeGreaterThan(90);
      expect(data.suggestions).toHaveLength(0);
    });

    it('should return low scores for poorly optimized content', async () => {
      const mockResponse = {
        suggestions: [
          {
            type: 'content',
            suggestion: 'Content appears to be quite short. Consider expanding with more detailed information.',
            reason: 'Content has 5 words. Aim for at least 300 words for better SEO performance.',
            impact: 'high'
          },
          {
            type: 'title',
            suggestion: 'Test - Complete Guide',
            reason: 'Title is quite short. Consider adding descriptive keywords to improve SEO.',
            impact: 'medium'
          }
        ],
        score: 15,
        metrics: {
          readability: 50,
          keywordDensity: 0,
          wordCount: 5,
          titleLength: 4,
          metaLength: 0
        }
      };

      (supabase.functions.invoke as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const { data } = await supabase.functions.invoke('seo-optimizer', {
        body: {
          content: 'Very short content.',
          title: 'Test',
          excerpt: '',
          tags: []
        }
      });

      expect(data.score).toBeLessThan(30);
      expect(data.suggestions.length).toBeGreaterThan(0);
    });
  });
});
