import { describe, it, expect, vi } from 'vitest';

// Mock the supabase client
const mockSupabase = {
  functions: {
    invoke: vi.fn()
  }
};

vi.mock('@/integrations/supabase/client', () => ({
  supabase: mockSupabase
}));

describe('SEO Integration Tests', () => {
  it('should integrate with real-time SEO analysis', async () => {
    // Mock the enhanced SEO response
    const mockResponse = {
      suggestions: [
        {
          type: 'title',
          suggestion: 'Enhanced Title with Keywords',
          reason: 'Title optimization for better SEO',
          impact: 'medium'
        },
        {
          type: 'content',
          suggestion: 'Add more detailed content with examples',
          reason: 'Content length is below optimal for SEO',
          impact: 'high'
        }
      ],
      score: 75,
      metrics: {
        readability: 65,
        keywordDensity: 2.1,
        wordCount: 150,
        titleLength: 25,
        metaLength: 140
      }
    };

    mockSupabase.functions.invoke.mockResolvedValue({
      data: mockResponse,
      error: null
    });

    // Simulate the BlogFormSEOPanel calling the function
    const { data, error } = await mockSupabase.functions.invoke('seo-optimizer', {
      body: {
        content: 'Sample blog content about trekking in Nepal with sufficient detail.',
        title: 'Nepal Trekking Guide',
        excerpt: 'Complete guide to trekking in Nepal',
        tags: ['nepal', 'trekking', 'adventure']
      }
    });

    expect(error).toBeNull();
    expect(data).toEqual(mockResponse);
    expect(data.suggestions).toHaveLength(2);
    expect(data.score).toBe(75);
    expect(data.metrics).toBeDefined();
    expect(data.metrics.readability).toBe(65);
    expect(data.metrics.keywordDensity).toBe(2.1);
  });

  it('should handle different suggestion types correctly', async () => {
    const mockResponse = {
      suggestions: [
        {
          type: 'title',
          suggestion: 'Optimized Title',
          reason: 'Title needs improvement',
          impact: 'high'
        },
        {
          type: 'meta',
          suggestion: 'Better meta description',
          reason: 'Meta description too short',
          impact: 'medium'
        },
        {
          type: 'keyword',
          suggestion: 'Add relevant keywords',
          reason: 'Keyword density is low',
          impact: 'medium'
        },
        {
          type: 'content',
          suggestion: 'Improve content structure',
          reason: 'Content needs better organization',
          impact: 'high'
        }
      ],
      score: 60,
      metrics: {
        readability: 70,
        keywordDensity: 1.5,
        wordCount: 200,
        titleLength: 30,
        metaLength: 100
      }
    };

    mockSupabase.functions.invoke.mockResolvedValue({
      data: mockResponse,
      error: null
    });

    const { data } = await mockSupabase.functions.invoke('seo-optimizer', {
      body: {
        content: 'Test content',
        title: 'Test Title',
        excerpt: 'Test excerpt',
        tags: ['test']
      }
    });

    // Verify all suggestion types are present
    const suggestionTypes = data.suggestions.map((s: any) => s.type);
    expect(suggestionTypes).toContain('title');
    expect(suggestionTypes).toContain('meta');
    expect(suggestionTypes).toContain('keyword');
    expect(suggestionTypes).toContain('content');

    // Verify impact levels
    const impacts = data.suggestions.map((s: any) => s.impact);
    expect(impacts).toContain('high');
    expect(impacts).toContain('medium');
  });

  it('should provide actionable suggestions with proper structure', async () => {
    const mockResponse = {
      suggestions: [
        {
          type: 'title',
          suggestion: 'Complete Guide to Everest Base Camp Trek - Tips & Preparation',
          reason: 'Title should be more descriptive and include target keywords',
          impact: 'high'
        }
      ],
      score: 85,
      metrics: {
        readability: 80,
        keywordDensity: 2.5,
        wordCount: 500,
        titleLength: 55,
        metaLength: 155
      }
    };

    mockSupabase.functions.invoke.mockResolvedValue({
      data: mockResponse,
      error: null
    });

    const { data } = await mockSupabase.functions.invoke('seo-optimizer', {
      body: {
        content: 'Well-optimized content with proper structure and keywords',
        title: 'Everest Trek Guide',
        excerpt: 'Complete guide to Everest Base Camp trek with all essential information you need for a successful adventure in Nepal.',
        tags: ['everest', 'trek', 'nepal']
      }
    });

    const suggestion = data.suggestions[0];
    expect(suggestion).toHaveProperty('type');
    expect(suggestion).toHaveProperty('suggestion');
    expect(suggestion).toHaveProperty('reason');
    expect(suggestion).toHaveProperty('impact');
    
    expect(typeof suggestion.type).toBe('string');
    expect(typeof suggestion.suggestion).toBe('string');
    expect(typeof suggestion.reason).toBe('string');
    expect(['high', 'medium', 'low']).toContain(suggestion.impact);
  });

  it('should calculate SEO scores appropriately', async () => {
    // Test high score scenario
    const highScoreResponse = {
      suggestions: [],
      score: 95,
      metrics: {
        readability: 85,
        keywordDensity: 2.5,
        wordCount: 500,
        titleLength: 55,
        metaLength: 158
      }
    };

    mockSupabase.functions.invoke.mockResolvedValueOnce({
      data: highScoreResponse,
      error: null
    });

    const { data: highScore } = await mockSupabase.functions.invoke('seo-optimizer', {
      body: {
        content: 'Well-optimized content',
        title: 'Perfect SEO Title Length With Keywords Included',
        excerpt: 'This meta description is perfectly sized for search engines and contains relevant keywords that will help with SEO rankings.',
        tags: ['seo', 'optimization']
      }
    });

    expect(highScore.score).toBeGreaterThan(90);
    expect(highScore.suggestions).toHaveLength(0);

    // Test low score scenario
    const lowScoreResponse = {
      suggestions: [
        {
          type: 'content',
          suggestion: 'Expand content significantly',
          reason: 'Content too short',
          impact: 'high'
        }
      ],
      score: 25,
      metrics: {
        readability: 50,
        keywordDensity: 0,
        wordCount: 10,
        titleLength: 5,
        metaLength: 0
      }
    };

    mockSupabase.functions.invoke.mockResolvedValueOnce({
      data: lowScoreResponse,
      error: null
    });

    const { data: lowScore } = await mockSupabase.functions.invoke('seo-optimizer', {
      body: {
        content: 'Short content',
        title: 'Short',
        excerpt: '',
        tags: []
      }
    });

    expect(lowScore.score).toBeLessThan(30);
    expect(lowScore.suggestions.length).toBeGreaterThan(0);
  });
});
