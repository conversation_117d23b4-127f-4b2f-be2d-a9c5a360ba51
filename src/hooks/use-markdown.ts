import { useState, useEffect } from 'react';
import { processMarkdown, initializeHighlighter } from '@/utils/markdown';

interface UseMarkdownOptions {
  autoInitialize?: boolean;
}

export function useMarkdown(options: UseMarkdownOptions = {}) {
  const { autoInitialize = true } = options;
  const [isInitialized, setIsInitialized] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (autoInitialize) {
      initializeHighlighter()
        .then(() => setIsInitialized(true))
        .catch((err) => {
          console.error('Failed to initialize markdown highlighter:', err);
          setError(err);
        });
    }
  }, [autoInitialize]);

  const process = async (content: string) => {
    if (!isInitialized) {
      try {
        await initializeHighlighter();
        setIsInitialized(true);
      } catch (err) {
        console.error('Failed to initialize markdown highlighter:', err);
        setError(err as Error);
        throw err;
      }
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await processMarkdown(content);
      return result;
    } catch (err) {
      console.error('Failed to process markdown:', err);
      setError(err as Error);
      throw err;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    process,
    isInitialized,
    isProcessing,
    error,
  };
}

// Helper function to process markdown synchronously when we don't need the full hook functionality
export async function processMarkdownContent(content: string) {
  try {
    if (!content) return '';
    return await processMarkdown(content);
  } catch (error) {
    console.error('Error processing markdown:', error);
    return content;
  }
}
