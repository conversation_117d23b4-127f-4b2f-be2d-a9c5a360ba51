/**
 * React Hook for Enhanced SEO Optimization
 * Provides easy integration with the enhanced SEO optimizer
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  EnhancedSEOOptimizer,
  SEOOptimizationRequest,
  SEOOptimizationResponse,
  OptimizationStats,
  EnhancedOptimizerConfig
} from '@/lib/seo/enhanced-optimizer';

export interface UseEnhancedSEOOptimizerOptions {
  config?: EnhancedOptimizerConfig;
  autoRetry?: boolean;
  enableRealTimeAnalysis?: boolean;
  debounceMs?: number;
}

export interface SEOOptimizationState {
  isOptimizing: boolean;
  result: SEOOptimizationResponse | null;
  error: string | null;
  stats: OptimizationStats | null;
  health: { status: 'healthy' | 'degraded' | 'unhealthy'; details: any } | null;
}

export function useEnhancedSEOOptimizer(options: UseEnhancedSEOOptimizerOptions = {}) {
  const [state, setState] = useState<SEOOptimizationState>({
    isOptimizing: false,
    result: null,
    error: null,
    stats: null,
    health: null
  });

  const optimizerRef = useRef<EnhancedSEOOptimizer | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize optimizer
  useEffect(() => {
    const initializeOptimizer = async () => {
      try {
        console.log('🔧 Initializing SEO Optimizer...');

        // Check if API key is available
        const apiKey = options.config?.geminiApiKey || import.meta.env.VITE_GEMINI_API_KEY;
        if (!apiKey) {
          throw new Error('Gemini API key not found. Please check your environment variables.');
        }

        console.log('✅ API key found, creating optimizer...');
        optimizerRef.current = new EnhancedSEOOptimizer(options.config);
        console.log('✅ Optimizer created successfully');

        // Initial health check
        console.log('🔍 Performing health check...');
        const health = await optimizerRef.current.healthCheck();
        console.log('📊 Health check result:', health);

        setState(prev => ({ ...prev, health, error: null }));
      } catch (error) {
        console.error('❌ Failed to initialize SEO optimizer:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize optimizer';
        setState(prev => ({
          ...prev,
          error: errorMessage,
          health: { status: 'unhealthy', details: { error: errorMessage } }
        }));
      }
    };

    initializeOptimizer();

    return () => {
      if (optimizerRef.current) {
        optimizerRef.current.destroy();
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const optimizeContent = useCallback(async (request: SEOOptimizationRequest): Promise<SEOOptimizationResponse | null> => {
    if (!optimizerRef.current) {
      const error = 'Optimizer not initialized. Please check console for initialization errors.';
      console.error('❌ Optimizer not initialized when trying to optimize content');
      setState(prev => ({ ...prev, error }));
      throw new Error(error);
    }

    console.log('🚀 Starting content optimization...', { title: request.title, contentType: request.contentType });

    setState(prev => ({
      ...prev,
      isOptimizing: true,
      error: null,
      result: null
    }));

    try {
      const result = await optimizerRef.current.optimizeContent(request);
      console.log('✅ Optimization completed successfully:', result);

      const stats = optimizerRef.current.getStats();
      const health = await optimizerRef.current.healthCheck();

      setState(prev => ({
        ...prev,
        isOptimizing: false,
        result,
        stats,
        health
      }));

      return result;
    } catch (error) {
      console.error('❌ Optimization failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Optimization failed';

      let stats = null;
      let health = null;

      try {
        stats = optimizerRef.current.getStats();
        health = await optimizerRef.current.healthCheck();
      } catch (statsError) {
        console.error('Failed to get stats/health after optimization error:', statsError);
      }

      setState(prev => ({
        ...prev,
        isOptimizing: false,
        error: errorMessage,
        stats,
        health
      }));

      if (options.autoRetry && !errorMessage.includes('validation')) {
        console.log('Auto-retrying optimization...');
        // Retry after a delay
        setTimeout(() => {
          optimizeContent(request);
        }, 2000);
      }

      throw error;
    }
  }, [options.autoRetry]);

  const optimizeContentDebounced = useCallback((
    request: SEOOptimizationRequest,
    callback?: (result: SEOOptimizationResponse | null) => void
  ) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(async () => {
      try {
        const result = await optimizeContent(request);
        callback?.(result);
      } catch (error) {
        console.error('Debounced optimization failed:', error);
        callback?.(null);
      }
    }, options.debounceMs || 1000);
  }, [optimizeContent, options.debounceMs]);

  const getStats = useCallback((): OptimizationStats | null => {
    if (!optimizerRef.current) return null;
    return optimizerRef.current.getStats();
  }, []);

  const resetStats = useCallback(() => {
    if (!optimizerRef.current) return;
    optimizerRef.current.resetStats();
    setState(prev => ({ ...prev, stats: optimizerRef.current!.getStats() }));
  }, []);

  const checkHealth = useCallback(async () => {
    if (!optimizerRef.current) return null;

    try {
      const health = await optimizerRef.current.healthCheck();
      setState(prev => ({ ...prev, health }));
      return health;
    } catch (error) {
      const health = {
        status: 'unhealthy' as const,
        details: { error: error instanceof Error ? error.message : 'Health check failed' }
      };
      setState(prev => ({ ...prev, health }));
      return health;
    }
  }, []);

  // Real-time analysis for form inputs
  const analyzeRealTime = useCallback((
    content: string,
    title: string,
    excerpt?: string,
    tags?: string[]
  ) => {
    if (!options.enableRealTimeAnalysis) return;

    if (!content || !title) {
      setState(prev => ({ ...prev, result: null, error: null }));
      return;
    }

    const request: SEOOptimizationRequest = {
      content,
      title,
      excerpt,
      tags,
      contentType: 'blog',
      priority: 1 // Lower priority for real-time analysis
    };

    optimizeContentDebounced(request, (result) => {
      if (result) {
        setState(prev => ({ ...prev, result }));
      }
    });
  }, [options.enableRealTimeAnalysis, optimizeContentDebounced]);

  return {
    // State
    isOptimizing: state.isOptimizing,
    result: state.result,
    error: state.error,
    stats: state.stats,
    health: state.health,

    // Actions
    optimizeContent,
    optimizeContentDebounced,
    analyzeRealTime,
    getStats,
    resetStats,
    checkHealth,

    // Computed values
    isHealthy: state.health?.status === 'healthy',
    isDegraded: state.health?.status === 'degraded',
    isUnhealthy: state.health?.status === 'unhealthy',
    successRate: state.stats ?
      (state.stats.successfulOptimizations / state.stats.totalRequests) * 100 : 0,
    averageProcessingTime: state.stats?.averageProcessingTime || 0
  };
}

// Utility hook for batch optimization
export function useBatchSEOOptimizer(options: UseEnhancedSEOOptimizerOptions = {}) {
  const [batchState, setBatchState] = useState({
    isProcessing: false,
    progress: 0,
    total: 0,
    results: [] as SEOOptimizationResponse[],
    errors: [] as string[]
  });

  const optimizerRef = useRef<EnhancedSEOOptimizer | null>(null);

  useEffect(() => {
    optimizerRef.current = new EnhancedSEOOptimizer(options.config);
    return () => {
      if (optimizerRef.current) {
        optimizerRef.current.destroy();
      }
    };
  }, []);

  const optimizeBatch = useCallback(async (
    requests: SEOOptimizationRequest[],
    onProgress?: (progress: number, total: number) => void
  ) => {
    if (!optimizerRef.current) {
      throw new Error('Optimizer not initialized');
    }

    setBatchState({
      isProcessing: true,
      progress: 0,
      total: requests.length,
      results: [],
      errors: []
    });

    const results: SEOOptimizationResponse[] = [];
    const errors: string[] = [];

    for (let i = 0; i < requests.length; i++) {
      try {
        const result = await optimizerRef.current.optimizeContent(requests[i]);
        results.push(result);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Request ${i + 1}: ${errorMessage}`);
      }

      const progress = i + 1;
      setBatchState(prev => ({ ...prev, progress, results: [...results], errors: [...errors] }));
      onProgress?.(progress, requests.length);

      // Add delay between requests to respect rate limits
      if (i < requests.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setBatchState(prev => ({ ...prev, isProcessing: false }));
    return { results, errors };
  }, []);

  return {
    isProcessing: batchState.isProcessing,
    progress: batchState.progress,
    total: batchState.total,
    results: batchState.results,
    errors: batchState.errors,
    optimizeBatch
  };
}
