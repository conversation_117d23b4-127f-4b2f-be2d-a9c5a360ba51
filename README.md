# TrekNepalX Platform

A comprehensive trekking and travel platform for Nepal, featuring trek packages, blog content, and booking management.

## Key Features

- Trek Package Management
- Blog System
- Booking System
- Admin Dashboard
- Testimonials
- SEO Optimization Suite
- Image Management

## SEO Enhancement Features

The platform includes a comprehensive SEO optimization suite with:

- Content Freshness Monitoring
- Automated Schema.org Implementation
- AI-powered Image SEO
- Content Calendar Management

[View detailed SEO documentation](docs/seo-features.md)

## Tech Stack

- React
- TypeScript
- Supabase
- TailwindCSS
- Shadcn UI
- Lucide Icons
- Google Gemini API

## Getting Started

1. Clone the repository
```bash
git clone https://github.com/your-username/nepal-adventure-platform.git
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
```

4. Start development server
```bash
npm run dev
```

## Environment Variables

```bash
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_GEMINI_API_KEY=your-gemini-api-key
```

## Project Structure

```
├── src/
│   ├── components/     # React components
│   ├── hooks/         # Custom React hooks
│   ├── pages/         # Page components
│   ├── types/         # TypeScript type definitions
│   └── integrations/  # Third-party integrations
├── supabase/
│   ├── functions/     # Edge Functions
│   └── migrations/    # Database migrations
└── docs/             # Documentation
```

## Features

### Trek Packages
- Create and manage trek packages
- Pricing and duration management
- Itinerary builder
- Image gallery

### Blog System
- Create and edit blog posts
- Categories and tags
- SEO optimization
- Content freshness monitoring

### Booking System
- Inquiry management
- Status tracking
- Communication system
- Payment integration (coming soon)

### Admin Dashboard
- User management
- Content management
- SEO monitoring
- Analytics overview

### SEO Optimization
- Content freshness analysis
- Schema.org implementation
- Image optimization
- Content calendar
- SEO performance monitoring

## Testing

Run unit tests:
```bash
npm run test
```

Run integration tests:
```bash
npm run test:integration
```

Run SEO enhancement tests:
```bash
npm run test:seo
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## Documentation

- [API Documentation](docs/api.md)
- [Database Schema](docs/database.md)
- [SEO Features](docs/seo-features.md)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
