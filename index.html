
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TrekNepalX - Premium Trekking & Expeditions in Nepal | Everest Base Camp & More</title>
    <meta name="description" content="Discover the heart of the Himalayas with TrekNepalX. Expert-guided treks to Everest Base Camp, Annapurna Circuit, and more. Custom & group adventures with local expertise." />
    <meta name="author" content="TrekNepalX" />
    <meta name="keywords" content="nepal trekking, everest base camp, himalaya tours, nepal expeditions, mountain trekking, annapurna circuit, langtang valley, manaslu circuit" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="TrekNepalX - Premium Trekking & Expeditions in Nepal" />
    <meta property="og:description" content="Discover the heart of the Himalayas with TrekNepalX. Expert-guided treks to Everest Base Camp, Annapurna Circuit, and more." />
    <meta property="og:image" content="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=630&fit=crop" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="TrekNepalX - Premium Trekking & Expeditions in Nepal" />
    <meta name="twitter:description" content="Discover the heart of the Himalayas with TrekNepalX. Expert-guided treks with local expertise." />
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=630&fit=crop" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- JSON-LD Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "TravelAgency",
      "name": "TrekNepalX",
      "description": "Premium trekking and expedition company in Nepal specializing in Himalayan adventures",
      "url": "https://treknepalx.com",
      "telephone": "+977-1-4444444",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Thamel Marg",
        "addressLocality": "Kathmandu",
        "addressCountry": "Nepal",
        "postalCode": "44600"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": 27.7172,
        "longitude": 85.3240
      },
      "sameAs": [
        "https://facebook.com/treknepalx",
        "https://instagram.com/treknepalx",
        "https://twitter.com/treknepalx"
      ]
    }
    </script>

    <!-- Gemini SEO Engine Placeholder -->
    <script>
      // Future AI-powered meta tag optimization will be implemented here
      // This script will dynamically update meta tags based on page content
      console.log('TrekNepalX - SEO optimization ready for AI integration');
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
